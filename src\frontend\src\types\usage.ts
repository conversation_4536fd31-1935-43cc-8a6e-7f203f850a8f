// TypeScript interfaces for Group Usage Tracking

export type SourceType = 'git' | 'api' | 'file';

export interface GitSourceConfig {
  repoURL: string;
  branch: string;
  authType: string;
  token?: string;
  username?: string;
  password?: string;
  clonePath?: string;
}

export interface ApiSourceConfig {
  endpoint: string;
  method: string;
  headers?: Record<string, string>;
  bodyTemplate?: string;
  authType: string;
  username?: string;
  password?: string;
  token?: string;
  apiKey?: string;
  apiKeyHeader?: string;
  responsePath?: string;
}

export interface FileSourceConfig {
  basePath: string;
  recursive: boolean;
  fileTypes?: string[];
}

export interface UsageSource {
  id: string;
  name: string;
  type: SourceType;
  isActive: boolean;
  scanFrequency: number; // in seconds
  createdAt: string;
  updatedAt: string;
  includePatterns?: string[];
  excludePatterns?: string[];
  gitConfig?: GitSourceConfig;
  apiConfig?: ApiSourceConfig;
  fileConfig?: FileSourceConfig;
}

export interface UsageResult {
  id: string;
  groupName: string;
  sourceId: string;
  sourceName: string;
  sourceType: SourceType;
  filePath: string;
  lineNumber: number;
  context: string;
  matchType: string;
  detectedAt: string;
  repoId: string;
  fileSize?: number;
  fileType?: string;
  commitHash?: string;
  branch?: string;
  apiResponse?: string;
}

export interface UsageResultList {
  results: UsageResult[];
  total: number;
  page: number;
  pageSize: number;
}

export interface SourceScanFailure {
  sourceId: string;
  sourceName: string;
  error: string;
  failedAt: string;
}

export interface UsageScanStatus {
  groupName: string;
  sourcesTotal: number;
  sourcesScanned: number;
  inProgress: boolean;
  lastScanTime: string;
  completedSources: string[];
  pendingSources: string[];
  failedSources: SourceScanFailure[];
  repoId: string;
  totalUsages: number;
  scanDuration: number;
}

export interface UsageScanRequest {
  groupName: string;
  sourceIds?: string[];
  repoId: string;
  force: boolean;
}

export interface SourceStatus {
  available: boolean;
  lastChecked: string;
  error: string;
  metadata?: Record<string, any>;
}

export interface UsageStatistics {
  totalGroups: number;
  groupsWithUsage: number;
  totalUsages: number;
  sourcesConfigured: number;
  sourcesActive: number;
  lastScanTime: string;
}

// API Response types
export interface UsageSourcesResponse {
  sources: UsageSource[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UsageSourceStatisticsResponse {
  totalSources: number;
  activeSources: number;
  sourcesByType: Record<string, number>;
}

export interface TestConnectionResponse {
  success: boolean;
  status: SourceStatus;
}

export interface ScanResponse {
  success: boolean;
  message: string;
  scanId: string;
}

export interface ApiResponse {
  success: boolean;
  message: string;
}

// Form types for creating/editing sources
export interface UsageSourceFormData {
  name: string;
  type: SourceType;
  isActive: boolean;
  scanFrequency: number;
  includePatterns: string[];
  excludePatterns: string[];
  gitConfig?: Partial<GitSourceConfig>;
  apiConfig?: Partial<ApiSourceConfig>;
  fileConfig?: Partial<FileSourceConfig>;
}

// UI State types
export interface UsageSourcesState {
  sources: UsageSource[];
  loading: boolean;
  error: string | null;
  selectedSource: UsageSource | null;
  showForm: boolean;
  editingSource: UsageSource | null;
}

export interface GroupUsageState {
  results: UsageResult[];
  scanStatus: UsageScanStatus | null;
  loading: boolean;
  scanning: boolean;
  error: string | null;
  page: number;
  pageSize: number;
  total: number;
  filters: {
    sourceType?: SourceType;
    sourceId?: string;
  };
}

// Constants
export const SOURCE_TYPES: { value: SourceType; label: string; description: string }[] = [
  {
    value: 'git',
    label: 'Git Repository',
    description: 'Scan Git repositories for group references in configuration files'
  },
  {
    value: 'api',
    label: 'REST API',
    description: 'Query REST APIs for group usage information'
  },
  {
    value: 'file',
    label: 'File System',
    description: 'Scan local file system directories for group references'
  }
];

export const AUTH_TYPES = {
  git: [
    { value: 'none', label: 'No Authentication' },
    { value: 'token', label: 'Personal Access Token' },
    { value: 'basic', label: 'Username/Password' }
  ],
  api: [
    { value: 'none', label: 'No Authentication' },
    { value: 'basic', label: 'Basic Authentication' },
    { value: 'bearer', label: 'Bearer Token' },
    { value: 'api-key', label: 'API Key' }
  ]
};

export const HTTP_METHODS = [
  'GET',
  'POST',
  'PUT',
  'PATCH',
  'DELETE'
];

export const DEFAULT_SCAN_FREQUENCIES = [
  { value: 300, label: '5 minutes' },
  { value: 900, label: '15 minutes' },
  { value: 1800, label: '30 minutes' },
  { value: 3600, label: '1 hour' },
  { value: 7200, label: '2 hours' },
  { value: 21600, label: '6 hours' },
  { value: 43200, label: '12 hours' },
  { value: 86400, label: '24 hours' }
];

// Validation schemas
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormErrors {
  [key: string]: string;
}

// Helper functions
export const getSourceTypeIcon = (type: SourceType): string => {
  switch (type) {
    case 'git':
      return '🔗'; // Git icon
    case 'api':
      return '🌐'; // API icon
    case 'file':
      return '📁'; // File icon
    default:
      return '❓';
  }
};

export const getSourceTypeColor = (type: SourceType): string => {
  switch (type) {
    case 'git':
      return 'text-blue-600';
    case 'api':
      return 'text-green-600';
    case 'file':
      return 'text-orange-600';
    default:
      return 'text-gray-600';
  }
};

export const formatScanFrequency = (seconds: number): string => {
  const frequency = DEFAULT_SCAN_FREQUENCIES.find(f => f.value === seconds);
  if (frequency) {
    return frequency.label;
  }
  
  if (seconds < 60) {
    return `${seconds} seconds`;
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)} minutes`;
  } else if (seconds < 86400) {
    return `${Math.floor(seconds / 3600)} hours`;
  } else {
    return `${Math.floor(seconds / 86400)} days`;
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
};
