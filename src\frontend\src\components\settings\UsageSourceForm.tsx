import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Plus, X } from 'lucide-react';
import type { 
  UsageSource, 
  SourceType, 
  GitSourceConfig, 
  ApiSourceConfig, 
  FileSourceConfig,
  UsageSourceFormData 
} from '@/types/usage';
import { 
  SOURCE_TYPES, 
  AUTH_TYPES, 
  HTTP_METHODS, 
  DEFAULT_SCAN_FREQUENCIES,
  getSourceTypeIcon 
} from '@/types/usage';

interface UsageSourceFormProps {
  source?: UsageSource | null;
  onSubmit: (data: Omit<UsageSource, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
}

const UsageSourceForm: React.FC<UsageSourceFormProps> = ({ source, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState<UsageSourceFormData>({
    name: '',
    type: 'git',
    isActive: true,
    scanFrequency: 3600, // 1 hour default
    includePatterns: [],
    excludePatterns: [],
    gitConfig: {
      repoURL: '',
      branch: 'main',
      authType: 'none',
    },
    apiConfig: {
      endpoint: '',
      method: 'GET',
      authType: 'none',
    },
    fileConfig: {
      basePath: '',
      recursive: true,
    },
  });

  const [newIncludePattern, setNewIncludePattern] = useState('');
  const [newExcludePattern, setNewExcludePattern] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when source prop changes
  useEffect(() => {
    if (source) {
      setFormData({
        name: source.name,
        type: source.type,
        isActive: source.isActive,
        scanFrequency: source.scanFrequency,
        includePatterns: source.includePatterns || [],
        excludePatterns: source.excludePatterns || [],
        gitConfig: source.gitConfig || {
          repoURL: '',
          branch: 'main',
          authType: 'none',
        },
        apiConfig: source.apiConfig || {
          endpoint: '',
          method: 'GET',
          authType: 'none',
        },
        fileConfig: source.fileConfig || {
          basePath: '',
          recursive: true,
        },
      });
    }
  }, [source]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (formData.scanFrequency < 300) {
      newErrors.scanFrequency = 'Scan frequency must be at least 5 minutes (300 seconds)';
    }

    // Type-specific validation
    switch (formData.type) {
      case 'git':
        if (!formData.gitConfig?.repoURL?.trim()) {
          newErrors.gitRepoURL = 'Repository URL is required';
        }
        if (formData.gitConfig?.authType === 'token' && !formData.gitConfig?.token?.trim()) {
          newErrors.gitToken = 'Token is required for token authentication';
        }
        if (formData.gitConfig?.authType === 'basic') {
          if (!formData.gitConfig?.username?.trim()) {
            newErrors.gitUsername = 'Username is required for basic authentication';
          }
          if (!formData.gitConfig?.password?.trim()) {
            newErrors.gitPassword = 'Password is required for basic authentication';
          }
        }
        break;

      case 'api':
        if (!formData.apiConfig?.endpoint?.trim()) {
          newErrors.apiEndpoint = 'API endpoint is required';
        }
        if (formData.apiConfig?.authType === 'basic') {
          if (!formData.apiConfig?.username?.trim()) {
            newErrors.apiUsername = 'Username is required for basic authentication';
          }
          if (!formData.apiConfig?.password?.trim()) {
            newErrors.apiPassword = 'Password is required for basic authentication';
          }
        }
        if (formData.apiConfig?.authType === 'bearer' && !formData.apiConfig?.token?.trim()) {
          newErrors.apiToken = 'Token is required for bearer authentication';
        }
        if (formData.apiConfig?.authType === 'api-key') {
          if (!formData.apiConfig?.apiKey?.trim()) {
            newErrors.apiKey = 'API key is required';
          }
          if (!formData.apiConfig?.apiKeyHeader?.trim()) {
            newErrors.apiKeyHeader = 'API key header name is required';
          }
        }
        break;

      case 'file':
        if (!formData.fileConfig?.basePath?.trim()) {
          newErrors.fileBasePath = 'Base path is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Prepare the data for submission
    const submitData: Omit<UsageSource, 'id' | 'createdAt' | 'updatedAt'> = {
      name: formData.name.trim(),
      type: formData.type,
      isActive: formData.isActive,
      scanFrequency: formData.scanFrequency,
      includePatterns: formData.includePatterns,
      excludePatterns: formData.excludePatterns,
    };

    // Add type-specific configuration
    switch (formData.type) {
      case 'git':
        submitData.gitConfig = formData.gitConfig;
        break;
      case 'api':
        submitData.apiConfig = formData.apiConfig;
        break;
      case 'file':
        submitData.fileConfig = formData.fileConfig;
        break;
    }

    onSubmit(submitData);
  };

  const updateFormData = (updates: Partial<UsageSourceFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const updateGitConfig = (updates: Partial<GitSourceConfig>) => {
    setFormData(prev => ({
      ...prev,
      gitConfig: { ...prev.gitConfig!, ...updates }
    }));
  };

  const updateApiConfig = (updates: Partial<ApiSourceConfig>) => {
    setFormData(prev => ({
      ...prev,
      apiConfig: { ...prev.apiConfig!, ...updates }
    }));
  };

  const updateFileConfig = (updates: Partial<FileSourceConfig>) => {
    setFormData(prev => ({
      ...prev,
      fileConfig: { ...prev.fileConfig!, ...updates }
    }));
  };

  const addIncludePattern = () => {
    if (newIncludePattern.trim()) {
      updateFormData({
        includePatterns: [...formData.includePatterns, newIncludePattern.trim()]
      });
      setNewIncludePattern('');
    }
  };

  const removeIncludePattern = (index: number) => {
    updateFormData({
      includePatterns: formData.includePatterns.filter((_, i) => i !== index)
    });
  };

  const addExcludePattern = () => {
    if (newExcludePattern.trim()) {
      updateFormData({
        excludePatterns: [...formData.excludePatterns, newExcludePattern.trim()]
      });
      setNewExcludePattern('');
    }
  };

  const removeExcludePattern = (index: number) => {
    updateFormData({
      excludePatterns: formData.excludePatterns.filter((_, i) => i !== index)
    });
  };

  const renderSourceTypeConfig = () => {
    switch (formData.type) {
      case 'git':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="gitRepoURL">Repository URL *</Label>
              <Input
                id="gitRepoURL"
                value={formData.gitConfig?.repoURL || ''}
                onChange={(e) => updateGitConfig({ repoURL: e.target.value })}
                placeholder="https://github.com/user/repo.git"
                className={errors.gitRepoURL ? 'border-red-500' : ''}
              />
              {errors.gitRepoURL && (
                <p className="text-sm text-red-500 mt-1">{errors.gitRepoURL}</p>
              )}
            </div>

            <div>
              <Label htmlFor="gitBranch">Branch</Label>
              <Input
                id="gitBranch"
                value={formData.gitConfig?.branch || ''}
                onChange={(e) => updateGitConfig({ branch: e.target.value })}
                placeholder="main"
              />
            </div>

            <div>
              <Label htmlFor="gitAuthType">Authentication Type</Label>
              <Select
                value={formData.gitConfig?.authType || 'none'}
                onValueChange={(value) => updateGitConfig({ authType: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {AUTH_TYPES.git.map((auth) => (
                    <SelectItem key={auth.value} value={auth.value}>
                      {auth.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.gitConfig?.authType === 'token' && (
              <div>
                <Label htmlFor="gitToken">Personal Access Token *</Label>
                <Input
                  id="gitToken"
                  type="password"
                  value={formData.gitConfig?.token || ''}
                  onChange={(e) => updateGitConfig({ token: e.target.value })}
                  placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                  className={errors.gitToken ? 'border-red-500' : ''}
                />
                {errors.gitToken && (
                  <p className="text-sm text-red-500 mt-1">{errors.gitToken}</p>
                )}
              </div>
            )}

            {formData.gitConfig?.authType === 'basic' && (
              <>
                <div>
                  <Label htmlFor="gitUsername">Username *</Label>
                  <Input
                    id="gitUsername"
                    value={formData.gitConfig?.username || ''}
                    onChange={(e) => updateGitConfig({ username: e.target.value })}
                    className={errors.gitUsername ? 'border-red-500' : ''}
                  />
                  {errors.gitUsername && (
                    <p className="text-sm text-red-500 mt-1">{errors.gitUsername}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="gitPassword">Password *</Label>
                  <Input
                    id="gitPassword"
                    type="password"
                    value={formData.gitConfig?.password || ''}
                    onChange={(e) => updateGitConfig({ password: e.target.value })}
                    className={errors.gitPassword ? 'border-red-500' : ''}
                  />
                  {errors.gitPassword && (
                    <p className="text-sm text-red-500 mt-1">{errors.gitPassword}</p>
                  )}
                </div>
              </>
            )}
          </div>
        );

      case 'api':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="apiEndpoint">API Endpoint *</Label>
              <Input
                id="apiEndpoint"
                value={formData.apiConfig?.endpoint || ''}
                onChange={(e) => updateApiConfig({ endpoint: e.target.value })}
                placeholder="https://api.example.com/data"
                className={errors.apiEndpoint ? 'border-red-500' : ''}
              />
              {errors.apiEndpoint && (
                <p className="text-sm text-red-500 mt-1">{errors.apiEndpoint}</p>
              )}
            </div>

            <div>
              <Label htmlFor="apiMethod">HTTP Method</Label>
              <Select
                value={formData.apiConfig?.method || 'GET'}
                onValueChange={(value) => updateApiConfig({ method: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {HTTP_METHODS.map((method) => (
                    <SelectItem key={method} value={method}>
                      {method}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="apiAuthType">Authentication Type</Label>
              <Select
                value={formData.apiConfig?.authType || 'none'}
                onValueChange={(value) => updateApiConfig({ authType: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {AUTH_TYPES.api.map((auth) => (
                    <SelectItem key={auth.value} value={auth.value}>
                      {auth.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* API Authentication fields based on type */}
            {formData.apiConfig?.authType === 'basic' && (
              <>
                <div>
                  <Label htmlFor="apiUsername">Username *</Label>
                  <Input
                    id="apiUsername"
                    value={formData.apiConfig?.username || ''}
                    onChange={(e) => updateApiConfig({ username: e.target.value })}
                    className={errors.apiUsername ? 'border-red-500' : ''}
                  />
                  {errors.apiUsername && (
                    <p className="text-sm text-red-500 mt-1">{errors.apiUsername}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="apiPassword">Password *</Label>
                  <Input
                    id="apiPassword"
                    type="password"
                    value={formData.apiConfig?.password || ''}
                    onChange={(e) => updateApiConfig({ password: e.target.value })}
                    className={errors.apiPassword ? 'border-red-500' : ''}
                  />
                  {errors.apiPassword && (
                    <p className="text-sm text-red-500 mt-1">{errors.apiPassword}</p>
                  )}
                </div>
              </>
            )}

            {formData.apiConfig?.authType === 'bearer' && (
              <div>
                <Label htmlFor="apiToken">Bearer Token *</Label>
                <Input
                  id="apiToken"
                  type="password"
                  value={formData.apiConfig?.token || ''}
                  onChange={(e) => updateApiConfig({ token: e.target.value })}
                  placeholder="Bearer token"
                  className={errors.apiToken ? 'border-red-500' : ''}
                />
                {errors.apiToken && (
                  <p className="text-sm text-red-500 mt-1">{errors.apiToken}</p>
                )}
              </div>
            )}

            {formData.apiConfig?.authType === 'api-key' && (
              <>
                <div>
                  <Label htmlFor="apiKey">API Key *</Label>
                  <Input
                    id="apiKey"
                    type="password"
                    value={formData.apiConfig?.apiKey || ''}
                    onChange={(e) => updateApiConfig({ apiKey: e.target.value })}
                    className={errors.apiKey ? 'border-red-500' : ''}
                  />
                  {errors.apiKey && (
                    <p className="text-sm text-red-500 mt-1">{errors.apiKey}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="apiKeyHeader">API Key Header *</Label>
                  <Input
                    id="apiKeyHeader"
                    value={formData.apiConfig?.apiKeyHeader || ''}
                    onChange={(e) => updateApiConfig({ apiKeyHeader: e.target.value })}
                    placeholder="X-API-Key"
                    className={errors.apiKeyHeader ? 'border-red-500' : ''}
                  />
                  {errors.apiKeyHeader && (
                    <p className="text-sm text-red-500 mt-1">{errors.apiKeyHeader}</p>
                  )}
                </div>
              </>
            )}

            <div>
              <Label htmlFor="apiResponsePath">Response Path (JSONPath)</Label>
              <Input
                id="apiResponsePath"
                value={formData.apiConfig?.responsePath || ''}
                onChange={(e) => updateApiConfig({ responsePath: e.target.value })}
                placeholder="$.data.groups"
              />
              <p className="text-sm text-muted-foreground mt-1">
                JSONPath expression to extract data from API response
              </p>
            </div>
          </div>
        );

      case 'file':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="fileBasePath">Base Path *</Label>
              <Input
                id="fileBasePath"
                value={formData.fileConfig?.basePath || ''}
                onChange={(e) => updateFileConfig({ basePath: e.target.value })}
                placeholder="/path/to/files"
                className={errors.fileBasePath ? 'border-red-500' : ''}
              />
              {errors.fileBasePath && (
                <p className="text-sm text-red-500 mt-1">{errors.fileBasePath}</p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="fileRecursive"
                checked={formData.fileConfig?.recursive || false}
                onCheckedChange={(checked) => updateFileConfig({ recursive: checked })}
              />
              <Label htmlFor="fileRecursive">Scan subdirectories recursively</Label>
            </div>

            <div>
              <Label htmlFor="fileTypes">File Types</Label>
              <Input
                id="fileTypes"
                value={formData.fileConfig?.fileTypes?.join(', ') || ''}
                onChange={(e) => updateFileConfig({ 
                  fileTypes: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                })}
                placeholder=".yaml, .json, .txt"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Comma-separated list of file extensions to scan (leave empty for all files)
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Sources
        </Button>
        <div>
          <h2 className="text-2xl font-bold">
            {source ? 'Edit Usage Source' : 'Add Usage Source'}
          </h2>
          <p className="text-muted-foreground">
            Configure an external source to scan for group usage references
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Configuration</CardTitle>
            <CardDescription>
              General settings for the usage source
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => updateFormData({ name: e.target.value })}
                placeholder="My Git Repository"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="type">Source Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value: SourceType) => updateFormData({ type: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SOURCE_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center">
                        <span className="mr-2">{getSourceTypeIcon(type.value)}</span>
                        <div>
                          <div>{type.label}</div>
                          <div className="text-xs text-muted-foreground">{type.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="scanFrequency">Scan Frequency</Label>
              <Select
                value={formData.scanFrequency.toString()}
                onValueChange={(value) => updateFormData({ scanFrequency: parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {DEFAULT_SCAN_FREQUENCIES.map((freq) => (
                    <SelectItem key={freq.value} value={freq.value.toString()}>
                      {freq.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.scanFrequency && (
                <p className="text-sm text-red-500 mt-1">{errors.scanFrequency}</p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => updateFormData({ isActive: checked })}
              />
              <Label htmlFor="isActive">Active (enable scanning)</Label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Source Configuration</CardTitle>
            <CardDescription>
              Settings specific to the {formData.type} source type
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderSourceTypeConfig()}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>File Patterns</CardTitle>
            <CardDescription>
              Configure which files to include or exclude from scanning
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Include Patterns</Label>
              <div className="flex space-x-2 mb-2">
                <Input
                  value={newIncludePattern}
                  onChange={(e) => setNewIncludePattern(e.target.value)}
                  placeholder="*.yaml"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addIncludePattern())}
                />
                <Button type="button" onClick={addIncludePattern} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.includePatterns.map((pattern, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center">
                    {pattern}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1"
                      onClick={() => removeIncludePattern(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Files matching these patterns will be scanned (empty = all files)
              </p>
            </div>

            <div>
              <Label>Exclude Patterns</Label>
              <div className="flex space-x-2 mb-2">
                <Input
                  value={newExcludePattern}
                  onChange={(e) => setNewExcludePattern(e.target.value)}
                  placeholder="*.log"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addExcludePattern())}
                />
                <Button type="button" onClick={addExcludePattern} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.excludePatterns.map((pattern, index) => (
                  <Badge key={index} variant="outline" className="flex items-center">
                    {pattern}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1"
                      onClick={() => removeExcludePattern(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Files matching these patterns will be excluded from scanning
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            {source ? 'Update Source' : 'Create Source'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default UsageSourceForm;
