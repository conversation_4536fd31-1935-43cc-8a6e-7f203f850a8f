import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  Search, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader,
  X,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';

import { scanLogsApi } from '@/api/client';
import type {
  ScanLogEntry,
  ScanLogSummary,
  ScanLogLevel
} from '@/types/scanLogs';
import {
  LogLevelColors,
  formatDuration,
  formatTimestamp,
  formatRelativeTime,
  getLogLevelText
} from '@/types/scanLogs';

interface GroupScanLogsProps {
  repoId: string;
  groupName: string;
  className?: string;
}

const GroupScanLogs: React.FC<GroupScanLogsProps> = ({ repoId, groupName, className }) => {
  const [logs, setLogs] = useState<ScanLogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<ScanLogLevel | ''>('');
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Load group-specific scan logs
  const loadLogs = useCallback(async () => {
    try {
      setError(null);
      const response = await scanLogsApi.getGroupLogs(repoId, groupName, {
        level: selectedLevel || undefined,
        page: 1,
        pageSize: 20 // Show recent logs only
      });
      setLogs(response.logs);
    } catch (err) {
      console.error('Failed to load group scan logs:', err);
      setError('Failed to load scan logs');
      setLogs([]);
    }
  }, [repoId, groupName, selectedLevel]);

  // Initial load
  useEffect(() => {
    if (expanded) {
      setLoading(true);
      loadLogs().finally(() => setLoading(false));
    }
  }, [expanded, loadLogs]);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh || !expanded) return;

    const interval = setInterval(() => {
      loadLogs();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, expanded, loadLogs]);

  // Manual refresh
  const handleRefresh = useCallback(async () => {
    setLoading(true);
    try {
      await loadLogs();
    } finally {
      setLoading(false);
    }
  }, [loadLogs]);

  // Get log level icon
  const getLogLevelIcon = (level: ScanLogLevel) => {
    switch (level) {
      case 'info':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'error':
        return <XCircle className="h-4 w-4" />;
      case 'debug':
        return <Search className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-lg">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
              className="p-0 h-auto mr-2"
            >
              {expanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            Scan Logs
            {logs.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {logs.length} recent
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {expanded && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm">Auto-refresh:</label>
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    className="rounded"
                  />
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      {expanded && (
        <CardContent>
          {/* Filters */}
          <div className="flex items-center space-x-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">Level</label>
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value as ScanLogLevel | '')}
                className="border rounded px-3 py-1 text-sm"
              >
                <option value="">All Levels</option>
                <option value="info">Info</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="debug">Debug</option>
              </select>
            </div>
            
            <div className="flex-1" />
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/settings?tab=scan-logs&repoId=${repoId}&groupName=${groupName}`, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View All Logs
            </Button>
          </div>

          {/* Error display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <div className="flex items-center text-red-700">
                <XCircle className="h-4 w-4 mr-2" />
                {error}
              </div>
            </div>
          )}

          {/* Loading state */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader className="h-6 w-6 animate-spin mr-2" />
              <span>Loading scan logs...</span>
            </div>
          )}

          {/* Logs list */}
          {!loading && (
            <div className="space-y-3">
              {logs.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>No scan logs found for this group.</p>
                  <p className="text-sm">Logs will appear here after running a usage scan.</p>
                </div>
              ) : (
                logs.map((log) => (
                  <div
                    key={log.id}
                    className="border rounded-lg p-3 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={`${LogLevelColors[log.level]} bg-transparent border text-xs`}>
                            <span className="flex items-center">
                              {getLogLevelIcon(log.level)}
                              <span className="ml-1">{getLogLevelText(log.level)}</span>
                            </span>
                          </Badge>
                          
                          {log.step && (
                            <Badge variant="outline" className="text-xs">
                              {log.step}
                            </Badge>
                          )}
                          
                          {log.sourceName && (
                            <Badge variant="secondary" className="text-xs">
                              {log.sourceName}
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm font-medium mb-1">{log.message}</p>
                        
                        {log.details && (
                          <p className="text-xs text-gray-600 mb-2">{log.details}</p>
                        )}
                        
                        {log.error && (
                          <p className="text-xs text-red-600 bg-red-50 p-2 rounded mb-2">
                            {log.error}
                          </p>
                        )}
                        
                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                          <span>{formatTimestamp(log.timestamp)}</span>
                          {log.duration && (
                            <span>Duration: {formatDuration(log.duration)}</span>
                          )}
                        </div>
                      </div>
                      
                      {log.progress && (
                        <div className="ml-3 text-right">
                          <div className="text-xs font-medium">
                            {log.progress.current} / {log.progress.total}
                          </div>
                          <div className="w-16 bg-gray-200 rounded-full h-1.5 mt-1">
                            <div
                              className="bg-blue-600 h-1.5 rounded-full transition-all"
                              style={{ width: `${log.progress.percentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          )}

          {/* Show more link */}
          {logs.length >= 20 && (
            <div className="text-center mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`/settings?tab=scan-logs&repoId=${repoId}&groupName=${groupName}`, '_blank')}
              >
                View All Logs ({logs.length}+ available)
              </Button>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default GroupScanLogs;
