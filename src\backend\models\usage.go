package models

import (
	"time"
)

// SourceType represents the type of usage source
type SourceType string

const (
	SourceTypeGit  SourceType = "git"
	SourceTypeAPI  SourceType = "api"
	SourceTypeFile SourceType = "file"
)

// UsageSource represents a configured source for group usage tracking
type UsageSource struct {
	ID            string     `json:"id"`            // Unique identifier
	Name          string     `json:"name"`          // Display name
	Type          SourceType `json:"type"`          // Source type (git, api, file)
	IsActive      bool       `json:"isActive"`      // Whether this source is active
	ScanFrequency int        `json:"scanFrequency"` // In seconds (minimum 300 = 5 minutes)
	CreatedAt     time.Time  `json:"createdAt"`     // Creation timestamp
	UpdatedAt     time.Time  `json:"updatedAt"`     // Last update timestamp

	// Common configuration
	IncludePatterns []string `json:"includePatterns,omitempty"` // File patterns to include (e.g., "*.yaml", "*.json")
	ExcludePatterns []string `json:"excludePatterns,omitempty"` // File patterns to exclude

	// Source-specific configuration
	GitConfig  *GitSourceConfig  `json:"gitConfig,omitempty"`
	ApiConfig  *ApiSourceConfig  `json:"apiConfig,omitempty"`
	FileConfig *FileSourceConfig `json:"fileConfig,omitempty"`
}

// GitSourceConfig contains Git-specific configuration
type GitSourceConfig struct {
	RepoURL   string `json:"repoURL"`               // Git repository URL
	Branch    string `json:"branch"`                // Git branch to scan (default: main/master)
	AuthType  string `json:"authType"`              // Auth type (token, basic, none)
	Token     string `json:"token,omitempty"`       // Git token
	Username  string `json:"username,omitempty"`    // Git username (for basic auth)
	Password  string `json:"password,omitempty"`    // Git password (for basic auth)
	ClonePath string `json:"clonePath,omitempty"`   // Local clone path (auto-generated)
}

// ApiSourceConfig contains API-specific configuration
type ApiSourceConfig struct {
	Endpoint     string            `json:"endpoint"`                  // API endpoint URL
	Method       string            `json:"method"`                    // HTTP method (GET, POST, etc.)
	Headers      map[string]string `json:"headers,omitempty"`         // HTTP headers
	BodyTemplate string            `json:"bodyTemplate,omitempty"`   // Request body template
	AuthType     string            `json:"authType"`                  // Auth type (none, basic, bearer, api-key)
	Username     string            `json:"username,omitempty"`        // API username
	Password     string            `json:"password,omitempty"`        // API password
	Token        string            `json:"token,omitempty"`           // API token
	ApiKey       string            `json:"apiKey,omitempty"`          // API key
	ApiKeyHeader string            `json:"apiKeyHeader,omitempty"`    // API key header name
	ResponsePath string            `json:"responsePath,omitempty"`    // JSON path to extract data from response
}

// FileSourceConfig contains file-specific configuration
type FileSourceConfig struct {
	BasePath  string   `json:"basePath"`              // Base directory path
	Recursive bool     `json:"recursive"`             // Whether to scan recursively
	FileTypes []string `json:"fileTypes,omitempty"`   // File extensions to scan
}

// UsageSourceList represents a list of usage sources
type UsageSourceList struct {
	Sources []UsageSource `json:"sources"`
}

// UsageResult represents a detected usage of a group
type UsageResult struct {
	ID         string     `json:"id"`         // Unique identifier
	GroupName  string     `json:"groupName"`  // Name of the group
	SourceID   string     `json:"sourceId"`   // ID of the source where usage was found
	SourceName string     `json:"sourceName"` // Name of the source
	SourceType SourceType `json:"sourceType"` // Type of source (git, api, file)
	FilePath   string     `json:"filePath"`   // Path to file (if applicable)
	LineNumber int        `json:"lineNumber"` // Line number (if applicable)
	Context    string     `json:"context"`    // Context around the usage (surrounding text)
	MatchType  string     `json:"matchType"`  // Type of match (exact, partial, regex)
	DetectedAt time.Time  `json:"detectedAt"` // When the usage was detected
	RepoID     string     `json:"repoId"`     // ID of the repository the group belongs to

	// Additional metadata
	FileSize    int64  `json:"fileSize,omitempty"`    // Size of the file where usage was found
	FileType    string `json:"fileType,omitempty"`    // Type/extension of the file
	CommitHash  string `json:"commitHash,omitempty"`  // Git commit hash (for git sources)
	Branch      string `json:"branch,omitempty"`      // Git branch (for git sources)
	ApiResponse string `json:"apiResponse,omitempty"` // API response snippet (for api sources)
}

// UsageResultList represents a list of usage results
type UsageResultList struct {
	Results  []UsageResult `json:"results"`
	Total    int           `json:"total"`    // Total number of results
	Page     int           `json:"page"`     // Current page (for pagination)
	PageSize int           `json:"pageSize"` // Page size
}

// UsageScanStatus represents the status of a usage scan
type UsageScanStatus struct {
	GroupName        string              `json:"groupName"`        // Name of the group
	SourcesTotal     int                 `json:"sourcesTotal"`     // Total number of sources
	SourcesScanned   int                 `json:"sourcesScanned"`   // Number of sources scanned
	InProgress       bool                `json:"inProgress"`       // Whether scan is in progress
	LastScanTime     time.Time           `json:"lastScanTime"`     // Last scan time
	CompletedSources []string            `json:"completedSources"` // IDs of completed sources
	PendingSources   []string            `json:"pendingSources"`   // IDs of pending sources
	FailedSources    []SourceScanFailure `json:"failedSources"`    // Sources that failed to scan
	RepoID           string              `json:"repoId"`           // ID of the repository
	TotalUsages      int                 `json:"totalUsages"`      // Total usages found
	ScanDuration     time.Duration       `json:"scanDuration"`     // Duration of the scan
}

// SourceScanFailure represents a failed source scan
type SourceScanFailure struct {
	SourceID   string    `json:"sourceId"`   // ID of the failed source
	SourceName string    `json:"sourceName"` // Name of the failed source
	Error      string    `json:"error"`      // Error message
	FailedAt   time.Time `json:"failedAt"`   // When the failure occurred
}

// UsageScanRequest represents a request to scan for group usage
type UsageScanRequest struct {
	GroupName string   `json:"groupName"`           // Name of the group to scan for
	SourceIDs []string `json:"sourceIds,omitempty"` // Specific source IDs to scan (empty = all active sources)
	RepoID    string   `json:"repoId"`              // ID of the repository
	Force     bool     `json:"force"`               // Force rescan even if recently scanned
}

// SourceStatus represents the status of a source
type SourceStatus struct {
	Available   bool                   `json:"available"`   // Whether the source is available
	LastChecked time.Time              `json:"lastChecked"` // When the status was last checked
	Error       string                 `json:"error"`       // Error message if not available
	Metadata    map[string]interface{} `json:"metadata,omitempty"` // Additional metadata
}

// UsageStatistics represents usage statistics
type UsageStatistics struct {
	TotalGroups       int       `json:"totalGroups"`       // Total number of groups
	GroupsWithUsage   int       `json:"groupsWithUsage"`   // Groups with detected usage
	TotalUsages       int       `json:"totalUsages"`       // Total usage occurrences
	SourcesConfigured int       `json:"sourcesConfigured"` // Number of configured sources
	SourcesActive     int       `json:"sourcesActive"`     // Number of active sources
	LastScanTime      time.Time `json:"lastScanTime"`      // Last scan time
}

// Validation methods

// Validate validates a UsageSource
func (us *UsageSource) Validate() error {
	if us.Name == "" {
		return NewValidationError("name", "name is required")
	}
	if us.Type == "" {
		return NewValidationError("type", "type is required")
	}
	if us.ScanFrequency < 300 {
		return NewValidationError("scanFrequency", "scan frequency must be at least 300 seconds (5 minutes)")
	}

	// Validate source-specific configuration
	switch us.Type {
	case SourceTypeGit:
		if us.GitConfig == nil {
			return NewValidationError("gitConfig", "git configuration is required for git sources")
		}
		return us.GitConfig.Validate()
	case SourceTypeAPI:
		if us.ApiConfig == nil {
			return NewValidationError("apiConfig", "api configuration is required for api sources")
		}
		return us.ApiConfig.Validate()
	case SourceTypeFile:
		if us.FileConfig == nil {
			return NewValidationError("fileConfig", "file configuration is required for file sources")
		}
		return us.FileConfig.Validate()
	default:
		return NewValidationError("type", "invalid source type")
	}
}

// Validate validates GitSourceConfig
func (gsc *GitSourceConfig) Validate() error {
	if gsc.RepoURL == "" {
		return NewValidationError("repoURL", "repository URL is required")
	}
	if gsc.Branch == "" {
		gsc.Branch = "main" // Default branch
	}
	if gsc.AuthType == "" {
		gsc.AuthType = "none" // Default auth type
	}
	return nil
}

// Validate validates ApiSourceConfig
func (asc *ApiSourceConfig) Validate() error {
	if asc.Endpoint == "" {
		return NewValidationError("endpoint", "endpoint URL is required")
	}
	if asc.Method == "" {
		asc.Method = "GET" // Default method
	}
	if asc.AuthType == "" {
		asc.AuthType = "none" // Default auth type
	}
	return nil
}

// Validate validates FileSourceConfig
func (fsc *FileSourceConfig) Validate() error {
	if fsc.BasePath == "" {
		return NewValidationError("basePath", "base path is required")
	}
	return nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (ve *ValidationError) Error() string {
	return ve.Message
}

// NewValidationError creates a new validation error
func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}
