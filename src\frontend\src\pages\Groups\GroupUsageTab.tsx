import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Loader2,
  Search,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  ExternalLink,
  Filter,
  X
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { apiClient } from '@/api/client';
import type {
  UsageResult,
  UsageResultList,
  UsageScanStatus,
  UsageScanRequest,
  SourceType
} from '@/types/usage';
import {
  getSourceTypeIcon,
  getSourceTypeColor,
  formatFileSize,
  formatTimeAgo,
  SOURCE_TYPES
} from '@/types/usage';
import { Group } from './GroupTypes';
import GroupScanLogs from '@/components/usage/GroupScanLogs';

interface GroupUsageTabProps {
  group: Group;
  repoId: string;
}

const GroupUsageTab: React.FC<GroupUsageTabProps> = ({ group, repoId }) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [usageResults, setUsageResults] = useState<UsageResult[]>([]);
  const [scanStatus, setScanStatus] = useState<UsageScanStatus | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(50);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<{
    sourceType?: SourceType;
    sourceId?: string;
    search?: string;
  }>({});

  // Load usage results and scan status on component mount
  useEffect(() => {
    loadUsageResults();
    loadScanStatus();
  }, [group.Groupname, repoId, page, filters]);

  // Poll scan status when scanning is in progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (scanStatus?.inProgress) {
      interval = setInterval(() => {
        loadScanStatus();
      }, 2000); // Poll every 2 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [scanStatus?.inProgress]);

  const loadUsageResults = async () => {
    try {
      setIsLoading(true);
      const response: UsageResultList = await apiClient.usageResults.getForGroup(
        repoId,
        group.Groupname,
        {
          page,
          pageSize,
          sourceType: filters.sourceType,
          sourceId: filters.sourceId,
        }
      );

      let results = response.results;

      // Apply client-side search filter if specified
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        results = results.filter(result =>
          result.filePath.toLowerCase().includes(searchTerm) ||
          result.context.toLowerCase().includes(searchTerm) ||
          result.sourceName.toLowerCase().includes(searchTerm)
        );
      }

      setUsageResults(results);
      setTotal(response.total);
    } catch (error) {
      console.error('Failed to load usage results:', error);
      setUsageResults([]);
      setTotal(0);
    } finally {
      setIsLoading(false);
    }
  };

  const loadScanStatus = async () => {
    try {
      const status = await apiClient.usageResults.getScanStatus(repoId, group.Groupname);
      setScanStatus(status);
      setIsScanning(status.inProgress);

      // If scan completed, reload results
      if (!status.inProgress && scanStatus?.inProgress) {
        loadUsageResults();
      }
    } catch (error) {
      // Scan status not found is expected for groups that haven't been scanned
      setScanStatus(null);
      setIsScanning(false);
    }
  };

  const handleManualScan = async () => {
    try {
      setIsScanning(true);
      const request: Omit<UsageScanRequest, 'groupName' | 'repoId'> = {
        force: true,
      };

      await apiClient.usageResults.scanGroup(repoId, group.Groupname, request);

      toast({
        title: 'Scan Started',
        description: `Usage scan started for group "${group.Groupname}"`,
      });

      // Start polling for status
      setTimeout(loadScanStatus, 1000);
    } catch (error) {
      console.error('Failed to start scan:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to start usage scan',
        variant: 'destructive',
      });
      setIsScanning(false);
    }
  };

  const handleClearResults = async () => {
    try {
      await apiClient.usageResults.clearResults(repoId, group.Groupname);
      toast({
        title: 'Results Cleared',
        description: `Usage results cleared for group "${group.Groupname}"`,
      });
      setUsageResults([]);
      setTotal(0);
      setScanStatus(null);
    } catch (error) {
      console.error('Failed to clear results:', error);
      toast({
        title: 'Clear Failed',
        description: 'Failed to clear usage results',
        variant: 'destructive',
      });
    }
  };

  const renderScanStatus = () => {
    if (!scanStatus) {
      return (
        <div className="flex items-center text-muted-foreground">
          <AlertCircle className="w-4 h-4 mr-2" />
          No scan completed yet
        </div>
      );
    }

    if (scanStatus.inProgress) {
      return (
        <div className="flex items-center text-blue-600">
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Scanning... ({scanStatus.sourcesScanned}/{scanStatus.sourcesTotal} sources)
        </div>
      );
    }

    const hasFailures = scanStatus.failedSources.length > 0;
    return (
      <div className="flex items-center space-x-4">
        <div className={`flex items-center ${hasFailures ? 'text-yellow-600' : 'text-green-600'}`}>
          {hasFailures ? (
            <AlertCircle className="w-4 h-4 mr-2" />
          ) : (
            <CheckCircle className="w-4 h-4 mr-2" />
          )}
          Scan completed
        </div>
        <div className="flex items-center text-muted-foreground">
          <Clock className="w-4 h-4 mr-1" />
          {formatTimeAgo(scanStatus.lastScanTime)}
        </div>
        <Badge variant="outline">
          {scanStatus.totalUsages} usage{scanStatus.totalUsages !== 1 ? 's' : ''} found
        </Badge>
      </div>
    );
  };

  const renderUsageResult = (result: UsageResult) => {
    return (
      <div key={result.id} className="border rounded-lg p-4 space-y-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">{result.filePath}</span>
            {result.lineNumber > 0 && (
              <Badge variant="outline" className="text-xs">
                Line {result.lineNumber}
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={getSourceTypeColor(result.sourceType)}>
              <span className="mr-1">{getSourceTypeIcon(result.sourceType)}</span>
              {result.sourceName}
            </Badge>
            {result.fileSize && (
              <span className="text-xs text-muted-foreground">
                {formatFileSize(result.fileSize)}
              </span>
            )}
          </div>
        </div>

        {result.context && (
          <div className="bg-gray-50 rounded p-3 text-sm font-mono whitespace-pre-wrap">
            {result.context}
          </div>
        )}

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            {result.fileType && (
              <span>{result.fileType.toUpperCase()} file</span>
            )}
            {result.commitHash && (
              <span>Commit: {result.commitHash.substring(0, 8)}</span>
            )}
            {result.branch && (
              <span>Branch: {result.branch}</span>
            )}
          </div>
          <span>Found {formatTimeAgo(result.detectedAt)}</span>
        </div>
      </div>
    );
  };

  const groupResultsBySource = (results: UsageResult[]) => {
    const grouped: Record<string, UsageResult[]> = {};
    results.forEach(result => {
      const key = `${result.sourceId}:${result.sourceName}`;
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(result);
    });
    return grouped;
  };

  const renderUsageResults = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </div>
      );
    }

    if (usageResults.length === 0) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground mb-4">
            {scanStatus ? 'No usage found for this group' : 'No scan completed yet'}
          </p>
          <Button onClick={handleManualScan} disabled={isScanning}>
            {isScanning ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            {scanStatus ? 'Scan Again' : 'Start Scan'}
          </Button>
        </div>
      );
    }

    const groupedResults = groupResultsBySource(usageResults);

    return (
      <div className="space-y-4">
        {Object.entries(groupedResults).map(([sourceKey, sourceResults]) => {
          const [sourceId, sourceName] = sourceKey.split(':');
          const sourceType = sourceResults[0].sourceType;

          return (
            <Collapsible key={sourceKey} defaultOpen>
              <CollapsibleTrigger asChild>
                <Card className="cursor-pointer hover:bg-gray-50">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
                        <span className="mr-2">{getSourceTypeIcon(sourceType)}</span>
                        <div>
                          <CardTitle className="text-base">{sourceName}</CardTitle>
                          <CardDescription>
                            {sourceResults.length} usage{sourceResults.length !== 1 ? 's' : ''} found
                          </CardDescription>
                        </div>
                      </div>
                      <Badge variant="outline" className={getSourceTypeColor(sourceType)}>
                        {sourceType.toUpperCase()}
                      </Badge>
                    </div>
                  </CardHeader>
                </Card>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="space-y-2 mt-2">
                  {sourceResults.map(renderUsageResult)}
                </div>
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-medium">Usage Tracking</h3>
          <p className="text-sm text-muted-foreground">
            External references to group "{group.Groupname}"
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {renderScanStatus()}
          <Button
            onClick={handleManualScan}
            disabled={isScanning}
            size="sm"
          >
            {isScanning ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            {isScanning ? 'Scanning...' : 'Scan Now'}
          </Button>
          {usageResults.length > 0 && (
            <Button
              onClick={handleClearResults}
              variant="outline"
              size="sm"
            >
              Clear Results
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search in file paths and content..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
            <Select
              value={filters.sourceType || 'all'}
              onValueChange={(value) => setFilters(prev => ({
                ...prev,
                sourceType: value === 'all' ? undefined : value as SourceType
              }))}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All source types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All source types</SelectItem>
                {SOURCE_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center">
                      <span className="mr-2">{getSourceTypeIcon(type.value)}</span>
                      {type.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {(filters.search || filters.sourceType) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({})}
              >
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {renderUsageResults()}

      {/* Scan Logs */}
      <GroupScanLogs
        repoId={repoId}
        groupName={group.Groupname}
        className="mt-6"
      />
    </div>
  );
};

export default GroupUsageTab;
