/**
 * API client for communicating with the backend
 */

import { API_CONFIG, PATH_CONFIG } from '@/configs';
import type {
  UsageSource,
  UsageResult,
  UsageResultList,
  UsageScanStatus,
  UsageScanRequest,
  UsageStatistics,
  UsageSourcesResponse,
  UsageSourceStatisticsResponse,
  TestConnectionResponse,
  ScanResponse,
  ApiResponse
} from '@/types/usage';

import type {
  ScanLogEntry,
  ScanLogSummary,
  ScanLogFilter,
  ScanLogResponse,
  ScanLogsOverview,
  ScanSummariesResponse
} from '@/types/scanLogs';

// Base URL for API requests
const API_BASE_URL = API_CONFIG.BASE_URL;

// Default options for fetch requests
const defaultOptions: RequestInit = {
  headers: {
    'Content-Type': 'application/json',
  },
};

// Cache for GET requests - stores responses with timestamps
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

const apiCache = new Map<string, CacheEntry<any>>();
const CACHE_TTL = 60000; // Cache time-to-live in milliseconds (1 minute)

// Store active request AbortControllers by endpoint
const activeRequests = new Map<string, AbortController>();

// Function to cancel an active request by endpoint
function cancelRequest(endpoint: string): boolean {
  const controller = activeRequests.get(endpoint);
  if (controller) {
    controller.abort();
    activeRequests.delete(endpoint);
    return true;
  }
  return false;
}

// Function to cancel all active requests for a specific pattern
// This is used by the cancelRequestsByPattern method in the utils object
const _cancelRequestsByPattern = (pattern: string): number => {
  let cancelCount = 0;
  activeRequests.forEach((controller, endpoint) => {
    if (endpoint.includes(pattern)) {
      controller.abort();
      activeRequests.delete(endpoint);
      cancelCount++;
          }
  });

  // Only log if we actually cancelled something
  if (cancelCount > 0) {
      }

  return cancelCount;
}

/**
 * Error class for API errors with additional details
 */
export class ApiError extends Error {
  status: number;
  data: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * Generic API request function with enhanced error handling and caching
 */
async function apiRequest<T>(
  endpoint: string,
  method: string = 'GET',
  data?: any,
  cancelPrevious: boolean = true
): Promise<T> {
  // Normalize the endpoint path to ensure it works on both Windows and Unix
  const normalizedEndpoint = PATH_CONFIG.normalize(endpoint);
  const url = `${API_BASE_URL}${normalizedEndpoint}`;

  // Cancel any existing request to this endpoint if cancelPrevious is true
  if (cancelPrevious) {
    cancelRequest(normalizedEndpoint);
  }

  // Create a new AbortController for this request
  const controller = new AbortController();
  activeRequests.set(normalizedEndpoint, controller);

  const options: RequestInit = {
    ...defaultOptions,
    method,
    signal: controller.signal, // Add the abort signal to the request
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  // Check cache for GET requests
  if (method === 'GET') {
    const cacheKey = url;
    const cachedEntry = apiCache.get(cacheKey);
    if (cachedEntry && Date.now() - cachedEntry.timestamp < CACHE_TTL) {
      // Remove from active requests since we're using cached data
      activeRequests.delete(normalizedEndpoint);
      return cachedEntry.data as T;
    }
  }

  try {
    const response = await fetch(url, options);

    // Remove from active requests after completion
    activeRequests.delete(normalizedEndpoint);

    const responseData = await response.json().catch((err) => {
      console.error('Error parsing JSON response:', err);
      return {};
    });

    if (!response.ok) {
      console.error(`API error (${response.status}) for ${method} ${url}:`, responseData);
      throw new ApiError(
        responseData.error || `API request failed with status ${response.status}`,
        response.status,
        responseData
      );
    }

    // Cache response for GET requests
    if (method === 'GET') {
      const cacheKey = url;
      apiCache.set(cacheKey, { data: responseData, timestamp: Date.now() });
    }

    return responseData;
  } catch (error) {
    // Remove from active requests in case of error
    activeRequests.delete(normalizedEndpoint);

    // Check if this is an abort error (request was cancelled)
    if (error instanceof DOMException && error.name === 'AbortError') {
            throw new ApiError('Request cancelled', -1, null);
    } else if (error instanceof ApiError) {
      throw error;
    } else if (error instanceof Error) {
      throw new ApiError(
        `Network error: ${error.message}`,
        0, // 0 for network errors
        null
      );
    } else {
      throw new ApiError('Unknown error occurred', 0, null);
    }
  }
}

/**
 * API client object with methods for each endpoint
 */
export const apiClient = {
  // Utility functions
  utils: {
    /**
     * Cancel an active request by endpoint
     */
    cancelRequest: (endpoint: string) => {
      return cancelRequest(PATH_CONFIG.normalize(endpoint));
    },

    /**
     * Cancel all active requests for a specific pattern
     */
    cancelRequestsByPattern: (pattern: string) => {
      return _cancelRequestsByPattern(pattern);
    },

    /**
     * Get the number of active requests
     */
    getActiveRequestCount: () => {
      return activeRequests.size;
    }
  },

  // Usage Sources API
  usageSources: {
    /**
     * Get all usage sources with optional filtering
     */
    getAll: (params?: { type?: string; active?: boolean; page?: number; pageSize?: number }) => {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params?.type) queryParams.set('type', params.type);
      if (params?.active !== undefined) queryParams.set('active', params.active.toString());
      if (params?.page) queryParams.set('page', params.page.toString());
      if (params?.pageSize) queryParams.set('pageSize', params.pageSize.toString());

      const queryString = queryParams.toString();
      const url = `/usage-sources${queryString ? `?${queryString}` : ''}`;

      return apiRequest<UsageSourcesResponse>(url);
    },

    /**
     * Get a specific usage source by ID
     */
    getById: (id: string) =>
      apiRequest<UsageSource>(`/usage-sources/${id}`),

    /**
     * Create a new usage source
     */
    create: (source: Omit<UsageSource, 'id' | 'createdAt' | 'updatedAt'>) =>
      apiRequest<UsageSource>('/usage-sources', 'POST', source),

    /**
     * Update an existing usage source
     */
    update: (id: string, source: UsageSource) =>
      apiRequest<UsageSource>(`/usage-sources/${id}`, 'PUT', source),

    /**
     * Delete a usage source
     */
    delete: (id: string) =>
      apiRequest<ApiResponse>(`/usage-sources/${id}`, 'DELETE'),

    /**
     * Test connection to a usage source
     */
    test: (id: string) =>
      apiRequest<TestConnectionResponse>(`/usage-sources/${id}/test`, 'POST'),

    /**
     * Get usage source statistics
     */
    getStatistics: () =>
      apiRequest<UsageSourceStatisticsResponse>('/usage-sources/statistics'),
  },

  // Usage Results API
  usageResults: {
    /**
     * Get usage results for a group
     */
    getForGroup: (repoId: string, groupName: string, params?: {
      page?: number;
      pageSize?: number;
      sourceType?: string;
      sourceId?: string;
    }) => {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.set('page', params.page.toString());
      if (params?.pageSize) queryParams.set('pageSize', params.pageSize.toString());
      if (params?.sourceType) queryParams.set('sourceType', params.sourceType);
      if (params?.sourceId) queryParams.set('sourceId', params.sourceId);

      const queryString = queryParams.toString();
      const url = `/usage-repo/${repoId}/groups/${groupName}/usage${queryString ? `?${queryString}` : ''}`;

      return apiRequest<UsageResultList>(url);
    },

    /**
     * Trigger a scan for a group
     */
    scanGroup: (repoId: string, groupName: string, request: Omit<UsageScanRequest, 'groupName' | 'repoId'>) =>
      apiRequest<ScanResponse>(`/usage-repo/${repoId}/groups/${groupName}/scan`, 'POST', request),

    /**
     * Get scan status for a group
     */
    getScanStatus: (repoId: string, groupName: string) =>
      apiRequest<UsageScanStatus>(`/usage-repo/${repoId}/groups/${groupName}/scan-status`),

    /**
     * Cancel ongoing scan for a group
     */
    cancelScan: (repoId: string, groupName: string) =>
      apiRequest<ApiResponse>(`/usage-repo/${repoId}/groups/${groupName}/scan`, 'DELETE'),

    /**
     * Clear usage results for a group
     */
    clearResults: (repoId: string, groupName: string) =>
      apiRequest<ApiResponse>(`/usage-repo/${repoId}/groups/${groupName}/usage`, 'DELETE'),

    /**
     * Get usage statistics for a repository
     */
    getStatistics: (repoId: string) =>
      apiRequest<UsageStatistics>(`/repo/${repoId}/usage-statistics`),
  },

  // Report execution methods moved to top level
  /**
   * Get report executions for a specific preset
   */
  getReportExecutionsByPreset: (presetId: string, repositoryId: string) => {
    return apiRequest<ReportExecution[]>(`/repo/${repositoryId}/reports/presets/${presetId}/executions`, 'GET', undefined, false);
  },

  // Repository API (type-agnostic)
  repositories: {
    /**
     * Get all repository configurations
     */
    getConfigurations: () =>
      apiRequest<RepositoryConfigList>('/repo/configurations'),

    /**
     * Get a specific repository configuration by ID
     */
    getConfiguration: (id: string) =>
      apiRequest<{ config: RepositoryConfig }>(`/repo/configurations/${id}`),

    /**
     * Add a new repository configuration
     */
    addConfiguration: (config: RepositoryConfig) =>
      apiRequest<AddConfigResponse>('/repo/configurations', 'POST', config),

    /**
     * Update an existing repository configuration
     */
    updateConfiguration: (id: string, config: RepositoryConfig) =>
      apiRequest<UpdateConfigResponse>(`/repo/configurations/${id}`, 'PUT', config),

    /**
     * Delete a repository configuration
     */
    deleteConfiguration: (id: string) =>
      apiRequest<GenericResponse>(`/repo/configurations/${id}`, 'DELETE'),

    /**
     * Get status for all repositories
     */
    getAllStatuses: () =>
      apiRequest<{ statuses: { [key: string]: RepositoryStatus } }>('/repo/status'),

    /**
     * Get status for a specific repository
     * @param repoId Repository ID
     */
    getRepositoryStatus: (repoId: string) =>
      apiRequest<{ status: RepositoryStatus }>(`/repo/status/${repoId}`),

    /**
     * Trigger a manual repository sync
     * @param repoId Repository ID
     */
    syncRepository: (repoId: string) =>
      apiRequest<SyncResponse>(`/repo/sync/${repoId}`, 'POST'),

    /**
     * Start repository polling
     */
    startPolling: (repoId: string) =>
      apiRequest<GenericResponse>(`/repo/polling/start/${repoId}`, 'POST'),

    /**
     * Stop repository polling
     */
    stopPolling: (repoId: string) =>
      apiRequest<GenericResponse>(`/repo/polling/stop/${repoId}`, 'POST'),

    /**
     * Get sync logs for a specific repository
     */
    getSyncLogs: (repoId: string) =>
      apiRequest<SyncLogResponse>(`/repo/sync-logs/${repoId}`),
  },

  // No backward compatibility needed anymore
  // All repository operations are now type-agnostic

  // Search API using Bleve
  search: {
    /**
     * Search for groups using Bleve search engine
     */
    searchGroups: (query: string, page?: number, pageSize?: number, repoId?: string, enhanced?: boolean) => {
      // Build the URL based on whether a repository ID is provided
      let baseUrl;
      if (repoId) {
        // Use repository-specific endpoint
        baseUrl = `/${repoId}/search/groups`;
      } else {
        // Use global search endpoint
        baseUrl = `/search/groups`;
      }

      const params = new URLSearchParams();

      // Add query parameter
      params.set('query', query);

      // Add pagination parameters
      if (page) {
        params.set('page', page.toString());
        params.set('pageSize', (pageSize || 20).toString());
      }

      // Add enhanced parameter if requested
      if (enhanced) {
        params.set('enhanced', 'true');
      }

      // Create the final URL with query string
      const queryString = params.toString();
      const url = `${baseUrl}?${queryString}`;

            return apiRequest<{groups: any[], total: number, page: number, pageSize: number, totalPages: number}>(url);
    },

    /**
     * Get enhanced membership information for a specific group
     */
    getGroupEnhanced: (groupName: string, repoId: string) => {
      const baseUrl = `/${repoId}/search/groups`;
      const params = new URLSearchParams();
      params.set('query', `groupname:${groupName}`);
      params.set('page', '1');
      params.set('pageSize', '1');
      params.set('enhanced', 'true');

      const url = `${baseUrl}?${params.toString()}`;
      return apiRequest<{groups: any[], total: number, page: number, pageSize: number, totalPages: number}>(url);
    },

    /**
     * Search for users using Bleve search engine
     */
    searchUsers: (query: string, page?: number, pageSize?: number, repoId?: string) => {
      // Build the URL based on whether a repository ID is provided
      let baseUrl;
      if (repoId) {
        // Use repository-specific endpoint
        baseUrl = `/${repoId}/search/users`;
      } else {
        // Use global search endpoint
        baseUrl = `/search/users`;
      }

      const params = new URLSearchParams();

      // Add query parameter
      params.set('query', query);

      // Add pagination parameters
      if (page) {
        params.set('page', page.toString());
        params.set('pageSize', (pageSize || 20).toString());
      }

      // Create the final URL with query string
      const queryString = params.toString();
      const url = `${baseUrl}?${queryString}`;

            return apiRequest<{users: any[], total: number, page: number, pageSize: number, totalPages: number}>(url);
    },

    /**
     * Get search index statistics
     */
    getStats: (repoId?: string) => {
      // Build the URL based on whether a repository ID is provided
      let url;
      if (repoId) {
        // Use repository-specific endpoint
        url = `/${repoId}/search/stats`;
      } else {
        // Use global search endpoint
        url = `/search/stats`;
      }

            return apiRequest<any>(url);
    },

    /**
     * Trigger reindexing of search data
     */
    reindex: (repoId?: string) => {
      // Build the URL based on whether a repository ID is provided
      let url;
      if (repoId) {
        // Use repository-specific endpoint
        url = `/${repoId}/search/reindex`;
      } else {
        // Use global search endpoint
        url = `/search/reindex`;
      }

            return apiRequest<{message: string}>(url, 'POST');
    }
  },

  // Data API
  data: {
    /**
     * Get all groups from a specific repository
     */
    getGroups: (repoId: string, page?: number, pageSize?: number, search?: string) => {
      // Base URL
      const baseUrl = `/data/repositories/${repoId}/groups`;
      const params = new URLSearchParams();

      // Add pagination parameters
      if (page) {
        params.set('page', page.toString());
        params.set('pageSize', (pageSize || 10).toString());
      }

      // Add search parameter
      if (search) {
        params.set('search', search);
      }

      // Create the final URL with query string
      const queryString = params.toString();
      const url = `${baseUrl}${queryString ? `?${queryString}` : ''}`;

            return apiRequest<{items: any[], pagination: PaginationInfo}>(url);
    },

    /**
     * Get groups filtered by LOB from a specific repository
     */
    getGroupsByLOB: (repoId: string, lob: string, page?: number, pageSize?: number, search?: string) => {
      // Base URL
      const baseUrl = `/data/repositories/${repoId}/groups/${lob}`;
      const params = new URLSearchParams();

      // Add pagination parameters
      if (page) {
        params.set('page', page.toString());
        params.set('pageSize', (pageSize || 10).toString());
      }

      // Add search parameter
      if (search) {
        params.set('search', search);
      }

      // Create the final URL with query string
      const queryString = params.toString();
      const url = `${baseUrl}${queryString ? `?${queryString}` : ''}`;

            return apiRequest<{items: any[], pagination: PaginationInfo}>(url);
    },

    /**
     * Get all users with their group memberships from a specific repository
     */
    getUsers: (repoId: string, page?: number, pageSize?: number, search?: string) => {
      // Base URL
      const baseUrl = `/data/repositories/${repoId}/users`;
      const params = new URLSearchParams();

      // Add pagination parameters
      if (page) {
        params.set('page', page.toString());
        params.set('pageSize', (pageSize || 10).toString());
      }

      // Add search parameter
      if (search) {
        params.set('search', search);
      }

      // Create the final URL with query string
      const queryString = params.toString();
      const url = `${baseUrl}${queryString ? `?${queryString}` : ''}`;

            return apiRequest<{items: any[], pagination: PaginationInfo}>(url);
    },

    /**
     * Get unique users with their group memberships for a specific group
     */
    getUniqueUsersForGroup: (repoId: string, groupName: string) => {
      const baseUrl = `/data/repositories/${repoId}/group-users/${encodeURIComponent(groupName)}`;
      return apiRequest<{users: any[], total: number}>(baseUrl);
    },

    /**
     * Get users filtered by LOB from a specific repository
     */
    getUsersByLOB: (repoId: string, lob: string, page?: number, pageSize?: number, search?: string) => {
      let url = `/data/repositories/${repoId}/users/${lob}`;
      const params = new URLSearchParams();

      if (page) {
        params.set('page', page.toString());
        params.set('pageSize', (pageSize || 10).toString());
      }

      if (search) {
        params.set('search', search);
      }

      const queryString = params.toString();
      return apiRequest<{items: any[], pagination: PaginationInfo}>(`${url}${queryString ? `?${queryString}` : ''}`);
    },

    // Export-related functions removed as they are now covered by the Report feature

    /**
     * Get all report presets for a repository
     * @param repositoryId Repository ID to filter presets
     */
    getReportPresets: (repositoryId: string) => {
      // Clear the cache for report presets to ensure we get fresh data
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets`);

      // Use the new repository-specific path with type-agnostic base
      return apiRequest<ReportPreset[]>(`/repo/${repositoryId}/reports/presets`, 'GET', undefined, false);
    },

    /**
     * Get a specific report preset
     * @param repositoryId Repository ID
     * @param presetId Preset ID
     */
    getReportPreset: (repositoryId: string, presetId: string) =>
      apiRequest<ReportPreset>(`/repo/${repositoryId}/reports/presets/${presetId}`),

    /**
     * Get all versions of a report preset
     * @param repositoryId Repository ID
     * @param presetId Preset ID
     */
    getReportPresetVersions: (repositoryId: string, presetId: string) =>
      apiRequest<ReportPreset[]>(`/repo/${repositoryId}/reports/presets/${presetId}/versions`),

    /**
     * Create a new report preset
     * @param repositoryId Repository ID
     * @param preset Preset data
     */
    createReportPreset: (repositoryId: string, preset: ReportPreset) => {
      // Clear the cache for report presets before making the request
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets`);
            return apiRequest<ReportPreset>(`/repo/${repositoryId}/reports/presets`, 'POST', preset);
    },

    /**
     * Update an existing report preset
     * @param repositoryId Repository ID
     * @param presetId Preset ID
     * @param preset Preset data
     */
    updateReportPreset: (repositoryId: string, presetId: string, preset: ReportPreset) => {
      // Clear the cache for report presets before making the request
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets`);
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets/${presetId}`);
      return apiRequest<ReportPreset>(`/repo/${repositoryId}/reports/presets/${presetId}`, 'PUT', preset);
    },

    /**
     * Toggle activation status of a report preset
     * @param repositoryId Repository ID
     * @param presetId Preset ID
     */
    togglePresetActivation: (repositoryId: string, presetId: string) => {
      // Clear the cache for report presets before making the request
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets`);
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets/${presetId}`);
      return apiRequest<ReportPreset>(`/repo/${repositoryId}/reports/presets/${presetId}/toggle-activation`, 'PUT');
    },

    /**
     * Deactivate a report preset (formerly delete)
     * @param repositoryId Repository ID
     * @param presetId Preset ID
     */
    deleteReportPreset: (repositoryId: string, presetId: string) => {
      // Clear the cache for report presets before making the request
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets`);
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports/presets/${presetId}`);
      return apiRequest<{ message: string }>(`/repo/${repositoryId}/reports/presets/${presetId}`, 'DELETE');
    },

    /**
     * Get all reports for a repository
     * @param repositoryId Repository ID
     */
    getReports: (repositoryId: string) => {
      // Clear the cache for reports to ensure we get fresh data
      apiCache.delete(`${API_BASE_URL}/repo/${repositoryId}/reports`);

      // Use the new repository-specific path with type-agnostic base
      return apiRequest<Report[]>(`/repo/${repositoryId}/reports`, 'GET', undefined, false);
    },

    /**
     * Generate a new report
     * @param repositoryId Repository ID
     * @param reportType Report type
     * @param query Query parameters
     */
    generateReport: (repositoryId: string, reportType: string, query: QueryParams) =>
      apiRequest<Report>(`/repo/${repositoryId}/reports/generate`, 'POST', { reportType, query }),

    /**
     * Generate a report from a preset
     * @param repositoryId Repository ID
     * @param presetId Preset ID
     */
    generateReportFromPreset: (repositoryId: string, presetId: string) =>
      apiRequest<Report>(`/repo/${repositoryId}/reports/generate/${presetId}`, 'POST', {}),

    /**
     * Delete a report
     * @param repositoryId Repository ID
     * @param reportId Report ID
     */
    deleteReport: (repositoryId: string, reportId: string) =>
      apiRequest<{ message: string }>(`/repo/${repositoryId}/reports/${reportId}`, 'DELETE'),

    /**
     * Batch delete multiple reports
     * @param repositoryId Repository ID
     * @param reportIds Report IDs to delete
     */
    batchDeleteReports: (repositoryId: string, reportIds: string[]) =>
      apiRequest<BatchDeleteResult>(`/repo/${repositoryId}/reports/batch-delete`, 'POST', { reportIds }),
  },
};

// Type definitions

// Repository type enum
export type RepositoryType = 'gitlab' | 'bitbucket';

// Base configuration for any repository
export interface RepositoryConfig {
  id?: string;
  name: string;
  type: RepositoryType;
  url: string; // lowercase to match backend model
  token: string; // lowercase to match backend model
  projectId: string; // lowercase to match backend model
  pollFrequency: number; // lowercase to match backend model
  isActive: boolean; // lowercase to match backend model
  localRepoDir?: string; // lowercase to match backend model

  // GitLab specific fields
  gitlabNamespace?: string; // lowercase to match backend model

  // Bitbucket specific fields
  bitbucketWorkspace?: string; // lowercase to match backend model
  bitbucketRepoSlug?: string; // lowercase to match backend model
  bitbucketUsername?: string; // lowercase to match backend model
  bitbucketPassword?: string; // lowercase to match backend model
}

// For backward compatibility
export interface GitLabConfig {
  id?: string;
  name: string;
  url: string; // lowercase to match backend model
  token: string; // lowercase to match backend model
  projectId: string; // lowercase to match backend model
  pollFrequency: number; // lowercase to match backend model
  isActive: boolean; // lowercase to match backend model
  localRepoDir?: string; // lowercase to match backend model
}

export interface RepositoryConfigList {
  configs: RepositoryConfig[];
}

export interface GitLabConfigList {
  configs: GitLabConfig[];
}

export interface RepositoryStatus {
  lastSync: string;
  lastCommit: string;
  hasChanges: boolean;
  syncInProgress: boolean;
}

export interface GenericResponse {
  message: string;
  status?: RepositoryStatus;
}

export interface SyncResponse extends GenericResponse {
  commitId?: string;
}

export interface UpdateConfigResponse {
  message?: string;
  config: RepositoryConfig;
}

export interface AddConfigResponse {
  message?: string;
  config: RepositoryConfig;
}

// Export-related interfaces removed as they are now covered by the Report feature

export interface Group {
  groupname: string;
  type: string;
  description: string;
  members: GroupMember;
  lob: string;
}

export type GroupMember = string[];

export interface User {
  username: string;
  groups: string[];
  lobs: string[];
}

export interface SyncLogResponse {
  logs: SyncLogEntry[];
}

export interface SyncLogEntry {
  timestamp: string;
  message: string;
  level: string;  // Backend sends this as 'info', 'warning', 'error', 'success'
  status?: string; // For backward compatibility
  repoId: string;
}

export interface QueryParams {
  lob?: string;
  types?: string[];
  groupIds?: string[];
  userIds?: string[];
  flattenMembership?: boolean; // Whether to recursively flatten group memberships to extract all users
}

export interface ScheduleConfig {
  enabled: boolean;
  frequency: 'interval' | 'daily' | 'weekly' | 'monthly';
  dayOfWeek?: number; // 0-6, Sunday-Saturday
  dayOfMonth?: number; // 1-31
  hour: number; // 0-23
  minute: number; // 0-59
  intervalHours?: number; // Hours component for interval frequency
  intervalMinutes?: number; // Minutes component for interval frequency
  nextRun?: string;
}

export interface ReportPreset {
  id?: string;
  name: string;
  description: string;
  reportType: 'users' | 'groups' | 'both';
  query: QueryParams;
  searchQuery?: string; // Raw search query for advanced filtering
  createdAt?: string;
  updatedAt?: string;
  isActive?: boolean; // Whether this preset is active or deactivated
  version?: number; // Version number of this preset
  parentId?: string; // ID of the original preset (for versioned presets)
  sharedId?: string; // Shared ID for all versions of the same preset
  schedule?: ScheduleConfig; // Configuration for automatic report generation
  repositoryId?: string; // ID of the repository this preset is bound to
}

export interface Report {
  id: string;
  presetId?: string;
  presetName?: string;
  presetVersion?: number; // Version of the preset used to generate this report
  sharedPresetId?: string; // Shared ID for all versions of the same preset
  filename: string;
  type: 'users' | 'groups' | 'both';
  size: number;
  createdAt: string;
  downloadUrl: string;
  metadata?: Record<string, any>; // Additional metadata about the report
  scheduled?: boolean; // Whether this report was generated by a schedule
  repositoryId?: string; // ID of the repository this report is bound to
}

export interface ReportExecution {
  id: string;
  presetId: string;
  presetName: string;
  presetVersion?: number; // Version of the preset that was executed
  sharedPresetId?: string; // Shared ID for all versions of the same preset
  status: 'success' | 'failed' | 'skipped';
  executedAt: string;
  reportId?: string; // ID of the generated report (if successful)
  errorMessage?: string; // Error message (if failed)
}

export interface BatchDeleteResult {
  totalCount: number;      // Total number of reports requested to delete
  successCount: number;    // Number of reports successfully deleted
  failedCount: number;     // Number of reports that failed to delete
  failedReportIds: Record<string, string>; // Map of report IDs to error messages for failed deletions
}

// Pagination information returned by API
interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

// Scan Logs API
export const scanLogsApi = {
  /**
   * Get scan logs with filtering and pagination
   */
  getLogs: (filter?: ScanLogFilter): Promise<ScanLogResponse> => {
    const params = new URLSearchParams();

    if (filter?.repoId) params.append('repoId', filter.repoId);
    if (filter?.groupName) params.append('groupName', filter.groupName);
    if (filter?.scanId) params.append('scanId', filter.scanId);
    if (filter?.level) params.append('level', filter.level);
    if (filter?.status) params.append('status', filter.status);
    if (filter?.sourceId) params.append('sourceId', filter.sourceId);
    if (filter?.searchQuery) params.append('search', filter.searchQuery);
    if (filter?.startTime) params.append('startTime', filter.startTime);
    if (filter?.endTime) params.append('endTime', filter.endTime);
    if (filter?.page) params.append('page', filter.page.toString());
    if (filter?.pageSize) params.append('pageSize', filter.pageSize.toString());

    const queryString = params.toString();
    const url = `/scan-logs${queryString ? `?${queryString}` : ''}`;

    console.log('API: Making request to scan logs endpoint:', url);
    return apiRequest<ScanLogResponse>(url);
  },

  /**
   * Get scan logs overview with statistics
   */
  getOverview: (): Promise<ScanLogsOverview> => {
    console.log('API: Making request to scan logs overview endpoint');
    return apiRequest<ScanLogsOverview>('/scan-logs/overview');
  },

  /**
   * Get scan summaries with filtering
   */
  getSummaries: (filter?: Partial<ScanLogFilter>): Promise<ScanSummariesResponse> => {
    const params = new URLSearchParams();

    if (filter?.repoId) params.append('repoId', filter.repoId);
    if (filter?.groupName) params.append('groupName', filter.groupName);
    if (filter?.status) params.append('status', filter.status);
    if (filter?.startTime) params.append('startTime', filter.startTime);
    if (filter?.endTime) params.append('endTime', filter.endTime);

    const queryString = params.toString();
    const url = `/scan-logs/summaries${queryString ? `?${queryString}` : ''}`;

    console.log('API: Making request to scan summaries endpoint:', url);
    return apiRequest<ScanSummariesResponse>(url);
  },

  /**
   * Get logs for a specific scan ID
   */
  getLogsByScanId: (scanId: string): Promise<ScanLogResponse> => {
    return apiRequest<ScanLogResponse>(`/scan-logs/${scanId}`);
  },

  /**
   * Clear logs for a specific scan
   */
  clearScanLogs: (scanId: string): Promise<ApiResponse> => {
    return apiRequest<ApiResponse>(`/scan-logs/${scanId}`, 'DELETE');
  },

  /**
   * Get scan logs for a specific repository
   */
  getRepoLogs: (repoId: string, filter?: Partial<ScanLogFilter>): Promise<ScanLogResponse> => {
    const params = new URLSearchParams();

    if (filter?.groupName) params.append('groupName', filter.groupName);
    if (filter?.level) params.append('level', filter.level);
    if (filter?.searchQuery) params.append('search', filter.searchQuery);
    if (filter?.page) params.append('page', filter.page.toString());
    if (filter?.pageSize) params.append('pageSize', filter.pageSize.toString());

    const queryString = params.toString();
    const url = `/usage-repo/${repoId}/scan-logs${queryString ? `?${queryString}` : ''}`;

    return apiRequest<ScanLogResponse>(url);
  },

  /**
   * Get scan logs for a specific group
   */
  getGroupLogs: (repoId: string, groupName: string, filter?: Partial<ScanLogFilter>): Promise<ScanLogResponse> => {
    const params = new URLSearchParams();

    if (filter?.level) params.append('level', filter.level);
    if (filter?.searchQuery) params.append('search', filter.searchQuery);
    if (filter?.page) params.append('page', filter.page.toString());
    if (filter?.pageSize) params.append('pageSize', filter.pageSize.toString());

    const queryString = params.toString();
    const url = `/usage-repo/${repoId}/scan-logs/groups/${groupName}${queryString ? `?${queryString}` : ''}`;

    return apiRequest<ScanLogResponse>(url);
  },

  /**
   * Cancel a running scan
   */
  cancelScan: (repoId: string, groupName: string): Promise<ApiResponse> => {
    return apiRequest<ApiResponse>(`/usage-repo/${repoId}/groups/${groupName}/scan`, 'DELETE');
  },
};

export default apiClient;
