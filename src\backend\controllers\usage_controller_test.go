package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockUsageSourceManager for testing
type MockUsageSourceManager struct {
	mock.Mock
}

func (m *MockUsageSourceManager) CreateSource(source models.UsageSource) (models.UsageSource, error) {
	args := m.Called(source)
	return args.Get(0).(models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) GetSource(id string) (models.UsageSource, error) {
	args := m.Called(id)
	return args.Get(0).(models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) GetAllSources() ([]models.UsageSource, error) {
	args := m.Called()
	return args.Get(0).([]models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) GetActiveSourcesByType(sourceType models.SourceType) ([]models.UsageSource, error) {
	args := m.Called(sourceType)
	return args.Get(0).([]models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) UpdateSource(source models.UsageSource) (models.UsageSource, error) {
	args := m.Called(source)
	return args.Get(0).(models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) DeleteSource(id string) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockUsageSourceManager) LoadSources() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockUsageSourceManager) SaveSources() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockUsageSourceManager) ValidateSource(source models.UsageSource) error {
	args := m.Called(source)
	return args.Error(0)
}

func (m *MockUsageSourceManager) TestSourceConnection(id string) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockUsageSourceManager) InvalidateCache() {
	m.Called()
}

// MockUsageScanner for testing
type MockUsageScanner struct {
	mock.Mock
}

func (m *MockUsageScanner) ScanGroupUsage(ctx context.Context, request models.UsageScanRequest) error {
	args := m.Called(ctx, request)
	return args.Error(0)
}

func (m *MockUsageScanner) GetScanStatus(groupName, repoID string) (models.UsageScanStatus, error) {
	args := m.Called(groupName, repoID)
	return args.Get(0).(models.UsageScanStatus), args.Error(1)
}

func (m *MockUsageScanner) GetUsageResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error) {
	args := m.Called(groupName, repoID, page, pageSize)
	return args.Get(0).(models.UsageResultList), args.Error(1)
}

func (m *MockUsageScanner) CancelScan(groupName, repoID string) error {
	args := m.Called(groupName, repoID)
	return args.Error(0)
}

func (m *MockUsageScanner) ClearUsageResults(groupName, repoID string) error {
	args := m.Called(groupName, repoID)
	return args.Error(0)
}

func (m *MockUsageScanner) GetUsageStatistics(repoID string) (models.UsageStatistics, error) {
	args := m.Called(repoID)
	return args.Get(0).(models.UsageStatistics), args.Error(1)
}

func setupUsageControllerTest() (*gin.Engine, *MockUsageSourceManager, *MockUsageScanner, *UsageController) {
	gin.SetMode(gin.TestMode)

	mockSourceManager := &MockUsageSourceManager{}
	mockScanner := &MockUsageScanner{}
	scanLogger := services.NewScanLogger()
	controller := NewUsageController(mockSourceManager, mockScanner, scanLogger)

	router := gin.New()
	api := router.Group("/api")
	controller.RegisterRoutes(api)

	return router, mockSourceManager, mockScanner, controller
}

func createTestUsageSource() models.UsageSource {
	return models.UsageSource{
		ID:            "test-source-1",
		Name:          "Test Git Source",
		Type:          models.SourceTypeGit,
		IsActive:      true,
		ScanFrequency: 300,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		GitConfig: &models.GitSourceConfig{
			RepoURL:  "https://github.com/test/repo.git",
			Branch:   "main",
			AuthType: "token",
			Token:    "test-token",
		},
	}
}

func TestUsageController_GetUsageSources(t *testing.T) {
	router, mockSourceManager, _, _ := setupUsageControllerTest()

	testSources := []models.UsageSource{
		createTestUsageSource(),
		{
			ID:       "test-source-2",
			Name:     "Test API Source",
			Type:     models.SourceTypeAPI,
			IsActive: false,
		},
	}

	mockSourceManager.On("GetAllSources").Return(testSources, nil)

	// Test getting all sources
	req, _ := http.NewRequest("GET", "/api/usage-sources", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	sources := response["sources"].([]interface{})
	assert.Len(t, sources, 2)
	assert.Equal(t, float64(2), response["total"])
	assert.Equal(t, float64(1), response["page"])
	assert.Equal(t, float64(50), response["pageSize"])
}

func TestUsageController_GetUsageSources_WithFilters(t *testing.T) {
	router, mockSourceManager, _, _ := setupUsageControllerTest()

	testSources := []models.UsageSource{
		createTestUsageSource(),
		{
			ID:       "test-source-2",
			Name:     "Test API Source",
			Type:     models.SourceTypeAPI,
			IsActive: false,
		},
	}

	mockSourceManager.On("GetAllSources").Return(testSources, nil)

	// Test filtering by type
	req, _ := http.NewRequest("GET", "/api/usage-sources?type=git", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	sources := response["sources"].([]interface{})
	assert.Len(t, sources, 1)
	assert.Equal(t, float64(1), response["total"])
}

func TestUsageController_GetUsageSource(t *testing.T) {
	router, mockSourceManager, _, _ := setupUsageControllerTest()

	testSource := createTestUsageSource()
	mockSourceManager.On("GetSource", "test-source-1").Return(testSource, nil)

	req, _ := http.NewRequest("GET", "/api/usage-sources/test-source-1", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.UsageSource
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, testSource.ID, response.ID)
	assert.Equal(t, testSource.Name, response.Name)
	assert.Equal(t, testSource.Type, response.Type)
}

func TestUsageController_CreateUsageSource(t *testing.T) {
	router, mockSourceManager, _, _ := setupUsageControllerTest()

	newSource := models.UsageSource{
		Name:          "New Test Source",
		Type:          models.SourceTypeGit,
		IsActive:      true,
		ScanFrequency: 600,
		GitConfig: &models.GitSourceConfig{
			RepoURL:  "https://github.com/test/new-repo.git",
			Branch:   "main",
			AuthType: "token",
			Token:    "new-token",
		},
	}

	createdSource := newSource
	createdSource.ID = "new-source-id"
	createdSource.CreatedAt = time.Now()
	createdSource.UpdatedAt = time.Now()

	mockSourceManager.On("CreateSource", mock.MatchedBy(func(source models.UsageSource) bool {
		return source.Name == newSource.Name && source.Type == newSource.Type
	})).Return(createdSource, nil)

	body, _ := json.Marshal(newSource)
	req, _ := http.NewRequest("POST", "/api/usage-sources", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)

	var response models.UsageSource
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, createdSource.ID, response.ID)
	assert.Equal(t, createdSource.Name, response.Name)
}

func TestUsageController_ScanGroupUsage(t *testing.T) {
	router, _, mockScanner, _ := setupUsageControllerTest()

	scanRequest := models.UsageScanRequest{
		SourceIDs: []string{"source-1", "source-2"},
		Force:     true,
	}

	mockScanner.On("ScanGroupUsage", mock.Anything, mock.MatchedBy(func(req models.UsageScanRequest) bool {
		return req.GroupName == "test-group" && req.RepoID == "test-repo"
	})).Return(nil)

	body, _ := json.Marshal(scanRequest)
	req, _ := http.NewRequest("POST", "/api/usage-repo/test-repo/groups/test-group/scan", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	assert.Contains(t, response["message"], "started successfully")
}

func TestUsageController_GetGroupUsage(t *testing.T) {
	router, _, mockScanner, _ := setupUsageControllerTest()

	testResults := models.UsageResultList{
		Results: []models.UsageResult{
			{
				ID:         "result-1",
				GroupName:  "test-group",
				SourceID:   "source-1",
				SourceName: "Test Source",
				SourceType: models.SourceTypeGit,
				FilePath:   "config.yaml",
				LineNumber: 10,
				Context:    "groups: [test-group]",
			},
		},
		Total:    1,
		Page:     1,
		PageSize: 50,
	}

	mockScanner.On("GetUsageResults", "test-group", "test-repo", 1, 50).Return(testResults, nil)

	req, _ := http.NewRequest("GET", "/api/usage-repo/test-repo/groups/test-group/usage", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.UsageResultList
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Len(t, response.Results, 1)
	assert.Equal(t, "test-group", response.Results[0].GroupName)
	assert.Equal(t, 1, response.Total)
}
