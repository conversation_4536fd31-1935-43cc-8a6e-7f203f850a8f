package services

import (
	"fmt"
	"log"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// ScanWorkerPool manages a pool of worker goroutines
type ScanWorkerPool struct {
	workers        int
	jobQueue       chan <PERSON><PERSON><PERSON><PERSON>
	resultsChannel chan <PERSON><PERSON><PERSON><PERSON><PERSON>
	done           chan struct{}
	wg             sync.WaitGroup
	isRunning      bool
	mutex          sync.Mutex
	statusManager  *ScanStatusManager

	// Performance optimizations
	batchSize    int           // Number of results to batch before processing
	batchTimeout time.Duration // Maximum time to wait for batch completion
}

// NewScanWorkerPool creates a new scan worker pool
func NewScanWorkerPool(workers int) *ScanWorkerPool {
	return &ScanWorkerPool{
		workers:        workers,
		jobQueue:       make(chan <PERSON><PERSON><PERSON><PERSON>, workers*2), // Buffer for 2x workers
		resultsChannel: make(chan <PERSON><PERSON><PERSON><PERSON><PERSON>, workers*2),
		done:           make(chan struct{}),
		isRunning:      false,
		batchSize:      10,              // Process results in batches of 10
		batchTimeout:   5 * time.Second, // Maximum 5 seconds between batches
	}
}

// SetStatusManager sets the status manager for the worker pool
func (p *ScanWorkerPool) SetStatusManager(statusManager *ScanStatusManager) {
	p.statusManager = statusManager
}

// Start starts the worker pool
func (p *ScanWorkerPool) Start() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.isRunning {
		return
	}

	p.isRunning = true

	// Start worker goroutines
	for i := 0; i < p.workers; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}

	// Start results processor
	p.wg.Add(1)
	go p.processResults()

	log.Printf("Started scan worker pool with %d workers", p.workers)
}

// Stop stops the worker pool
func (p *ScanWorkerPool) Stop() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isRunning {
		return
	}

	p.isRunning = false

	// Signal all goroutines to stop
	close(p.done)

	// Close job queue to signal workers to stop
	close(p.jobQueue)

	// Wait for all workers to finish
	p.wg.Wait()

	// Now it's safe to close results channel since all workers are done
	close(p.resultsChannel)

	log.Println("Stopped scan worker pool")
}

// SubmitJob submits a job to the pool
func (p *ScanWorkerPool) SubmitJob(job ScanJob) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isRunning {
		return fmt.Errorf("worker pool is not running")
	}

	select {
	case p.jobQueue <- job:
		log.Printf("Submitted scan job for group %s, source %s", job.GroupName, job.Source.Name)
		return nil
	default:
		return fmt.Errorf("job queue is full")
	}
}

// worker is the worker goroutine
func (p *ScanWorkerPool) worker(id int) {
	defer p.wg.Done()

	log.Printf("Worker %d started", id)

	for job := range p.jobQueue {
		log.Printf("Worker %d processing job for group %s, source %s", id, job.GroupName, job.Source.Name)

		startTime := time.Now()
		result := p.processJob(job)
		result.CompletedAt = time.Now()

		log.Printf("Worker %d completed job for group %s, source %s in %v",
			id, job.GroupName, job.Source.Name, result.CompletedAt.Sub(startTime))

		// Send result to results channel (check if channel is still open)
		select {
		case p.resultsChannel <- result:
		case <-job.Context.Done():
			log.Printf("Worker %d: context cancelled while sending result", id)
		default:
			// Channel might be closed, check if pool is still running
			p.mutex.Lock()
			if p.isRunning {
				// Pool is running but channel is full, try again with timeout
				select {
				case p.resultsChannel <- result:
				case <-time.After(100 * time.Millisecond):
					log.Printf("Worker %d: timeout sending result", id)
				}
			}
			p.mutex.Unlock()
		}
	}

	log.Printf("Worker %d stopped", id)
}

// processJob processes a single scan job
func (p *ScanWorkerPool) processJob(job ScanJob) ScanResult {
	result := ScanResult{
		GroupName: job.GroupName,
		RepoID:    job.RepoID,
		SourceID:  job.Source.ID,
		Results:   []models.UsageResult{},
		Error:     nil,
	}

	// Check if context is already cancelled
	select {
	case <-job.Context.Done():
		result.Error = job.Context.Err()
		return result
	default:
	}

	// Test connection first
	if err := job.Handler.TestConnection(job.Context); err != nil {
		log.Printf("Connection test failed for source %s: %v", job.Source.Name, err)
		result.Error = fmt.Errorf("connection test failed: %w", err)
		return result
	}

	// Perform the actual scan
	usageResults, err := job.Handler.ScanForGroupUsage(job.Context, job.GroupName)
	if err != nil {
		log.Printf("Scan failed for source %s: %v", job.Source.Name, err)
		result.Error = fmt.Errorf("scan failed: %w", err)
		return result
	}

	// Fill in missing fields in results
	for i := range usageResults {
		if usageResults[i].ID == "" {
			usageResults[i].ID = uuid.New().String()
		}
		if usageResults[i].GroupName == "" {
			usageResults[i].GroupName = job.GroupName
		}
		if usageResults[i].SourceID == "" {
			usageResults[i].SourceID = job.Source.ID
		}
		if usageResults[i].SourceName == "" {
			usageResults[i].SourceName = job.Source.Name
		}
		if usageResults[i].SourceType == "" {
			usageResults[i].SourceType = job.Source.Type
		}
		if usageResults[i].RepoID == "" {
			usageResults[i].RepoID = job.RepoID
		}
		if usageResults[i].DetectedAt.IsZero() {
			usageResults[i].DetectedAt = time.Now()
		}
	}

	result.Results = usageResults
	log.Printf("Found %d usage results for group %s in source %s", len(usageResults), job.GroupName, job.Source.Name)

	return result
}

// processResults processes scan results with batching for better performance
func (p *ScanWorkerPool) processResults() {
	defer p.wg.Done()

	log.Println("Results processor started")

	var batch []ScanResult
	batchTimer := time.NewTimer(p.batchTimeout)
	defer batchTimer.Stop()

	processBatch := func() {
		if len(batch) == 0 {
			return
		}

		log.Printf("Processing batch of %d results", len(batch))

		for _, result := range batch {
			if p.statusManager != nil {
				if result.Error != nil {
					// Handle failed scan
					log.Printf("Scan failed for group %s, source %s: %v", result.GroupName, result.SourceID, result.Error)

					// Update status with failure information
					if err := p.statusManager.UpdateSourceFailure(result.GroupName, result.RepoID, result.SourceID, result.Error.Error()); err != nil {
						log.Printf("Failed to update scan status with failure: %v", err)
					}
				} else {
					// Handle successful scan
					if err := p.statusManager.AddResults(result.GroupName, result.RepoID, result.SourceID, result.Results); err != nil {
						log.Printf("Failed to save scan results: %v", err)
					} else {
						log.Printf("Saved %d usage results for group %s, source %s", len(result.Results), result.GroupName, result.SourceID)
					}
				}
			} else {
				log.Printf("Warning: No status manager configured, results not saved")
			}
		}

		// Clear batch
		batch = batch[:0]
		batchTimer.Reset(p.batchTimeout)
	}

	for {
		select {
		case <-p.done:
			// Process any remaining results in batch before stopping
			processBatch()
			log.Println("Results processor stopped")
			return
		case result, ok := <-p.resultsChannel:
			if !ok {
				// Process any remaining results in batch before stopping
				processBatch()
				log.Println("Results processor stopped")
				return
			}

			// Add result to batch
			batch = append(batch, result)

			// Process batch if it's full
			if len(batch) >= p.batchSize {
				processBatch()
			}
		case <-batchTimer.C:
			// Process batch on timeout
			processBatch()
		}
	}
}

// GetQueueSize returns the current size of the job queue
func (p *ScanWorkerPool) GetQueueSize() int {
	return len(p.jobQueue)
}

// IsRunning returns whether the worker pool is running
func (p *ScanWorkerPool) IsRunning() bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	return p.isRunning
}
