package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
)

// ScanStatusManager manages scan status
type ScanStatusManager struct {
	statusMap      map[string]models.UsageScanStatus // Map of groupName:repoID to status
	resultsMap     map[string][]models.UsageResult   // Map of groupName:repoID to results
	heartbeatMap   map[string]time.Time              // Map of groupName:repoID to last heartbeat
	statusMutex    sync.RWMutex
	resultsMutex   sync.RWMutex
	heartbeatMutex sync.RWMutex
	statusDir      string        // Directory for status files
	resultsDir     string        // Directory for results files
	scanTimeout    time.Duration // Maximum time a scan can run before being considered orphaned
}

// NewScanStatusManager creates a new scan status manager
func NewScanStatusManager(dataDir string) *ScanStatusManager {
	statusDir := filepath.Join(dataDir, "usage-results", "status")
	resultsDir := filepath.Join(dataDir, "usage-results", "results")

	// Create directories
	if err := os.MkdirAll(statusDir, 0755); err != nil {
		log.Printf("Warning: Failed to create status directory: %v", err)
	}
	if err := os.MkdirAll(resultsDir, 0755); err != nil {
		log.Printf("Warning: Failed to create results directory: %v", err)
	}

	manager := &ScanStatusManager{
		statusMap:    make(map[string]models.UsageScanStatus),
		resultsMap:   make(map[string][]models.UsageResult),
		heartbeatMap: make(map[string]time.Time),
		statusDir:    statusDir,
		resultsDir:   resultsDir,
		scanTimeout:  30 * time.Minute, // Default 30 minute timeout for orphaned scans
	}

	// Load existing status and results
	manager.loadAllStatus()
	manager.loadAllResults()

	// Perform crash recovery on startup
	manager.recoverOrphanedScans()

	return manager
}

// GetStatus gets the status of a scan
func (m *ScanStatusManager) GetStatus(groupName, repoID string) (models.UsageScanStatus, error) {
	key := m.getKey(groupName, repoID)

	m.statusMutex.RLock()
	defer m.statusMutex.RUnlock()

	status, exists := m.statusMap[key]
	if !exists {
		// Try to load from disk
		if loadedStatus, err := m.loadStatusFromDisk(groupName, repoID); err == nil {
			return loadedStatus, nil
		}
		return models.UsageScanStatus{}, fmt.Errorf("scan status not found for group %s in repo %s", groupName, repoID)
	}

	return status, nil
}

// UpdateStatus updates the status of a scan
func (m *ScanStatusManager) UpdateStatus(status models.UsageScanStatus) error {
	key := m.getKey(status.GroupName, status.RepoID)

	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	m.statusMap[key] = status

	// If scan is no longer in progress, remove heartbeat
	if !status.InProgress {
		m.RemoveHeartbeat(status.GroupName, status.RepoID)
	}

	// Save to disk
	if err := m.saveStatusToDisk(status); err != nil {
		log.Printf("Warning: Failed to save status to disk: %v", err)
		return err
	}

	return nil
}

// AddResults adds results to a scan
func (m *ScanStatusManager) AddResults(groupName, repoID, sourceID string, results []models.UsageResult) error {
	key := m.getKey(groupName, repoID)

	m.resultsMutex.Lock()
	defer m.resultsMutex.Unlock()

	// Add results to memory
	if existing, exists := m.resultsMap[key]; exists {
		m.resultsMap[key] = append(existing, results...)
	} else {
		m.resultsMap[key] = results
	}

	// Update scan status
	m.statusMutex.Lock()
	if status, exists := m.statusMap[key]; exists {
		// Add to completed sources
		found := false
		for _, completedID := range status.CompletedSources {
			if completedID == sourceID {
				found = true
				break
			}
		}
		if !found {
			status.CompletedSources = append(status.CompletedSources, sourceID)
		}

		// Remove from pending sources
		var newPending []string
		for _, pendingID := range status.PendingSources {
			if pendingID != sourceID {
				newPending = append(newPending, pendingID)
			}
		}
		status.PendingSources = newPending

		// Update counters
		status.SourcesScanned = len(status.CompletedSources)
		status.TotalUsages += len(results)

		// Check if scan is complete
		if len(status.PendingSources) == 0 {
			status.InProgress = false
		}

		m.statusMap[key] = status

		// Save status to disk
		if err := m.saveStatusToDisk(status); err != nil {
			log.Printf("Warning: Failed to save updated status to disk: %v", err)
		}
	}
	m.statusMutex.Unlock()

	// Save results to disk
	if err := m.saveResultsToDisk(groupName, repoID, m.resultsMap[key]); err != nil {
		log.Printf("Warning: Failed to save results to disk: %v", err)
		return err
	}

	return nil
}

// GetResults gets the results of a scan
func (m *ScanStatusManager) GetResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error) {
	key := m.getKey(groupName, repoID)

	m.resultsMutex.RLock()
	defer m.resultsMutex.RUnlock()

	results, exists := m.resultsMap[key]
	if !exists {
		// Try to load from disk
		if loadedResults, err := m.loadResultsFromDisk(groupName, repoID); err == nil {
			results = loadedResults
		} else {
			results = []models.UsageResult{}
		}
	}

	// Apply pagination
	total := len(results)
	start := (page - 1) * pageSize
	if start < 0 {
		start = 0
	}
	if start >= total {
		return models.UsageResultList{
			Results:  []models.UsageResult{},
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		}, nil
	}

	end := start + pageSize
	if end > total {
		end = total
	}

	paginatedResults := results[start:end]

	return models.UsageResultList{
		Results:  paginatedResults,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// ClearResults clears the results of a scan
func (m *ScanStatusManager) ClearResults(groupName, repoID string) error {
	key := m.getKey(groupName, repoID)

	m.resultsMutex.Lock()
	delete(m.resultsMap, key)
	m.resultsMutex.Unlock()

	m.statusMutex.Lock()
	delete(m.statusMap, key)
	m.statusMutex.Unlock()

	// Remove files from disk
	statusFile := m.getStatusFilePath(groupName, repoID)
	resultsFile := m.getResultsFilePath(groupName, repoID)

	if err := os.Remove(statusFile); err != nil && !os.IsNotExist(err) {
		log.Printf("Warning: Failed to remove status file: %v", err)
	}

	if err := os.Remove(resultsFile); err != nil && !os.IsNotExist(err) {
		log.Printf("Warning: Failed to remove results file: %v", err)
	}

	return nil
}

// Helper methods

func (m *ScanStatusManager) getKey(groupName, repoID string) string {
	return fmt.Sprintf("%s:%s", groupName, repoID)
}

// UpdateSourceFailure updates the scan status when a source fails
func (m *ScanStatusManager) UpdateSourceFailure(groupName, repoID, sourceID, errorMessage string) error {
	key := m.getKey(groupName, repoID)

	// Update scan status
	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	if status, exists := m.statusMap[key]; exists {
		// Add to failed sources
		failure := models.SourceScanFailure{
			SourceID:   sourceID,
			SourceName: sourceID, // TODO: Get actual source name
			Error:      errorMessage,
			FailedAt:   time.Now(),
		}
		status.FailedSources = append(status.FailedSources, failure)

		// Remove from pending sources
		var newPending []string
		for _, pendingID := range status.PendingSources {
			if pendingID != sourceID {
				newPending = append(newPending, pendingID)
			}
		}
		status.PendingSources = newPending

		// Update counters
		status.SourcesScanned = len(status.CompletedSources) + len(status.FailedSources)

		// Check if scan is complete
		if len(status.PendingSources) == 0 {
			status.InProgress = false
		}

		m.statusMap[key] = status

		// Save status to disk
		if err := m.saveStatusToDisk(status); err != nil {
			log.Printf("Warning: Failed to save updated status to disk: %v", err)
			return err
		}
	}

	return nil
}

func (m *ScanStatusManager) getStatusFilePath(groupName, repoID string) string {
	filename := fmt.Sprintf("%s_%s_status.json", repoID, groupName)
	return filepath.Join(m.statusDir, filename)
}

func (m *ScanStatusManager) getResultsFilePath(groupName, repoID string) string {
	filename := fmt.Sprintf("%s_%s_results.json", repoID, groupName)
	return filepath.Join(m.resultsDir, filename)
}

func (m *ScanStatusManager) saveStatusToDisk(status models.UsageScanStatus) error {
	filePath := m.getStatusFilePath(status.GroupName, status.RepoID)

	data, err := json.MarshalIndent(status, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal status: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write status file: %w", err)
	}

	return nil
}

func (m *ScanStatusManager) loadStatusFromDisk(groupName, repoID string) (models.UsageScanStatus, error) {
	filePath := m.getStatusFilePath(groupName, repoID)

	data, err := os.ReadFile(filePath)
	if err != nil {
		return models.UsageScanStatus{}, fmt.Errorf("failed to read status file: %w", err)
	}

	var status models.UsageScanStatus
	if err := json.Unmarshal(data, &status); err != nil {
		return models.UsageScanStatus{}, fmt.Errorf("failed to unmarshal status: %w", err)
	}

	return status, nil
}

func (m *ScanStatusManager) saveResultsToDisk(groupName, repoID string, results []models.UsageResult) error {
	filePath := m.getResultsFilePath(groupName, repoID)

	resultsList := models.UsageResultList{
		Results:  results,
		Total:    len(results),
		Page:     1,
		PageSize: len(results),
	}

	data, err := json.MarshalIndent(resultsList, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal results: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write results file: %w", err)
	}

	return nil
}

func (m *ScanStatusManager) loadResultsFromDisk(groupName, repoID string) ([]models.UsageResult, error) {
	filePath := m.getResultsFilePath(groupName, repoID)

	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read results file: %w", err)
	}

	var resultsList models.UsageResultList
	if err := json.Unmarshal(data, &resultsList); err != nil {
		return nil, fmt.Errorf("failed to unmarshal results: %w", err)
	}

	return resultsList.Results, nil
}

func (m *ScanStatusManager) loadAllStatus() {
	// Load all status files from disk
	files, err := filepath.Glob(filepath.Join(m.statusDir, "*_status.json"))
	if err != nil {
		log.Printf("Warning: Failed to glob status files: %v", err)
		return
	}

	for _, file := range files {
		data, err := os.ReadFile(file)
		if err != nil {
			log.Printf("Warning: Failed to read status file %s: %v", file, err)
			continue
		}

		var status models.UsageScanStatus
		if err := json.Unmarshal(data, &status); err != nil {
			log.Printf("Warning: Failed to unmarshal status file %s: %v", file, err)
			continue
		}

		key := m.getKey(status.GroupName, status.RepoID)
		m.statusMap[key] = status
	}

	log.Printf("Loaded %d scan status records from disk", len(m.statusMap))
}

func (m *ScanStatusManager) loadAllResults() {
	// Load all results files from disk
	files, err := filepath.Glob(filepath.Join(m.resultsDir, "*_results.json"))
	if err != nil {
		log.Printf("Warning: Failed to glob results files: %v", err)
		return
	}

	for _, file := range files {
		data, err := os.ReadFile(file)
		if err != nil {
			log.Printf("Warning: Failed to read results file %s: %v", file, err)
			continue
		}

		var resultsList models.UsageResultList
		if err := json.Unmarshal(data, &resultsList); err != nil {
			log.Printf("Warning: Failed to unmarshal results file %s: %v", file, err)
			continue
		}

		// Extract groupName and repoID from filename
		// Filename format: {repoID}_{groupName}_results.json
		basename := filepath.Base(file)
		basename = basename[:len(basename)-len("_results.json")]

		// Find the first underscore to separate repoID and groupName
		parts := []string{}
		current := ""
		for i, char := range basename {
			if char == '_' && len(parts) == 0 {
				parts = append(parts, current)
				current = basename[i+1:]
				break
			}
			current += string(char)
		}
		if current != "" {
			parts = append(parts, current)
		}

		if len(parts) == 2 {
			repoID := parts[0]
			groupName := parts[1]
			key := m.getKey(groupName, repoID)
			m.resultsMap[key] = resultsList.Results
		}
	}

	log.Printf("Loaded %d scan results records from disk", len(m.resultsMap))
}

// UpdateHeartbeat updates the heartbeat for an active scan
func (m *ScanStatusManager) UpdateHeartbeat(groupName, repoID string) {
	key := m.getKey(groupName, repoID)

	m.heartbeatMutex.Lock()
	defer m.heartbeatMutex.Unlock()

	m.heartbeatMap[key] = time.Now()
}

// RemoveHeartbeat removes the heartbeat for a completed scan
func (m *ScanStatusManager) RemoveHeartbeat(groupName, repoID string) {
	key := m.getKey(groupName, repoID)

	m.heartbeatMutex.Lock()
	defer m.heartbeatMutex.Unlock()

	delete(m.heartbeatMap, key)
}

// recoverOrphanedScans detects and recovers orphaned scans on startup
func (m *ScanStatusManager) recoverOrphanedScans() {
	log.Println("Checking for orphaned scans...")

	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	orphanedCount := 0
	for key, status := range m.statusMap {
		if status.InProgress {
			// Check if this scan has been running too long
			timeSinceStart := time.Since(status.LastScanTime)
			if timeSinceStart > m.scanTimeout {
				log.Printf("Detected orphaned scan for %s (running for %v)", key, timeSinceStart)

				// Mark scan as failed due to crash
				status.InProgress = false
				status.FailedSources = append(status.FailedSources, models.SourceScanFailure{
					SourceID:   "system",
					SourceName: "System Recovery",
					Error:      fmt.Sprintf("Scan orphaned after application restart (running for %v)", timeSinceStart),
					FailedAt:   time.Now(),
				})

				// Update the status
				m.statusMap[key] = status

				// Save to disk
				if err := m.saveStatusToDisk(status); err != nil {
					log.Printf("Warning: Failed to save recovered status to disk: %v", err)
				}

				orphanedCount++
			}
		}
	}

	if orphanedCount > 0 {
		log.Printf("Recovered %d orphaned scans", orphanedCount)
	} else {
		log.Println("No orphaned scans found")
	}
}

// CheckForOrphanedScans periodically checks for orphaned scans during runtime
func (m *ScanStatusManager) CheckForOrphanedScans() {
	m.statusMutex.RLock()
	m.heartbeatMutex.RLock()

	var orphanedScans []string
	now := time.Now()

	for key, status := range m.statusMap {
		if status.InProgress {
			// Check if we have a recent heartbeat
			if lastHeartbeat, exists := m.heartbeatMap[key]; exists {
				if now.Sub(lastHeartbeat) > m.scanTimeout {
					orphanedScans = append(orphanedScans, key)
				}
			} else {
				// No heartbeat found for an in-progress scan
				timeSinceStart := now.Sub(status.LastScanTime)
				if timeSinceStart > m.scanTimeout {
					orphanedScans = append(orphanedScans, key)
				}
			}
		}
	}

	m.heartbeatMutex.RUnlock()
	m.statusMutex.RUnlock()

	// Handle orphaned scans
	if len(orphanedScans) > 0 {
		log.Printf("Found %d orphaned scans during runtime check", len(orphanedScans))

		m.statusMutex.Lock()
		for _, key := range orphanedScans {
			if status, exists := m.statusMap[key]; exists && status.InProgress {
				log.Printf("Marking orphaned scan as failed: %s", key)

				status.InProgress = false
				status.FailedSources = append(status.FailedSources, models.SourceScanFailure{
					SourceID:   "system",
					SourceName: "System Monitor",
					Error:      "Scan orphaned due to missing heartbeat",
					FailedAt:   time.Now(),
				})

				m.statusMap[key] = status

				// Save to disk
				if err := m.saveStatusToDisk(status); err != nil {
					log.Printf("Warning: Failed to save orphaned scan status to disk: %v", err)
				}

				// Remove heartbeat
				m.heartbeatMutex.Lock()
				delete(m.heartbeatMap, key)
				m.heartbeatMutex.Unlock()
			}
		}
		m.statusMutex.Unlock()
	}
}
