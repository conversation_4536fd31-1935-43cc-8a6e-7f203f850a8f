# Group Usage Tracking Feature - Technical Design

## Overview

This document outlines the technical design for implementing the Group Usage Tracking feature in the adgitops_ui application. This feature will allow users to discover where groups are referenced across multiple external sources, including Git repositories, REST API responses, and other configurable external sources.

## Existing Codebase Patterns

### Models

The application uses Go structs for data models with JSON tags for serialization:

```go
// Example of existing model pattern
type Group struct {
    Groupname       string           `json:"Groupname"`
    Type            string           `json:"Type"`
    Description     string           `json:"Description"`
    Members         GroupMembers     `json:"Members"`
    DirectMembers   *MembershipInfo  `json:"DirectMembers,omitempty"`
    ResolvedMembers *ResolvedMembers `json:"ResolvedMembers,omitempty"`
    ParentGroups    []string         `json:"ParentGroups,omitempty"`
    Lob             string           `json:"LOB"`
    SourceFile      string           `json:"SourceFile,omitempty"`
    RepoID          string           `json:"repoId,omitempty"`
}
```

### Storage

The application uses file-based storage for configurations and data:

1. Configuration files: `configs/repositories.json`
2. Data files: `data/` directory for various data storage

### Services

Services follow a pattern of:
1. Interface definition
2. Implementation struct
3. Constructor function
4. Method implementations

```go
// Example service pattern
type ServiceInterface interface {
    Method1() error
    Method2(param string) (Result, error)
}

type ServiceImpl struct {
    dependency1 Dependency1
    dependency2 Dependency2
}

func NewService(dep1 Dependency1, dep2 Dependency2) *ServiceImpl {
    return &ServiceImpl{
        dependency1: dep1,
        dependency2: dep2,
    }
}

func (s *ServiceImpl) Method1() error {
    // Implementation
}
```

### Controllers

Controllers use the Gin framework with route registration:

```go
// Controller pattern
type Controller struct {
    service Service
}

func NewController(service Service) *Controller {
    return &Controller{
        service: service,
    }
}

func (c *Controller) RegisterRoutes(router *gin.RouterGroup) {
    router.GET("/endpoint", c.handlerMethod)
    router.POST("/endpoint", c.anotherHandler)
}

func (c *Controller) handlerMethod(ctx *gin.Context) {
    // Implementation
    ctx.JSON(http.StatusOK, result)
}
```

### Background Processing

The application uses a scheduler service with:
1. Ticker-based scheduling
2. Goroutines for concurrent processing
3. Status tracking and logging

### Frontend

The frontend uses:
1. React with TypeScript
2. Component-based architecture
3. Tab-based UI for group details
4. shadcn/ui components
5. API client for backend communication

## New Feature Design

### Data Models

#### UsageSource

```go
// SourceType represents the type of usage source
type SourceType string

const (
    SourceTypeGit  SourceType = "git"
    SourceTypeAPI  SourceType = "api"
    SourceTypeFile SourceType = "file"
)

// UsageSource represents a configured source for group usage tracking
type UsageSource struct {
    ID          string     `json:"id"`            // Unique identifier
    Name        string     `json:"name"`          // Display name
    Type        SourceType `json:"type"`          // Source type (git, api, file)
    IsActive    bool       `json:"isActive"`      // Whether this source is active
    ScanFrequency int      `json:"scanFrequency"` // In seconds (minimum 300 = 5 minutes)
    CreatedAt   time.Time  `json:"createdAt"`     // Creation timestamp
    UpdatedAt   time.Time  `json:"updatedAt"`     // Last update timestamp

    // Common configuration
    IncludePatterns []string `json:"includePatterns,omitempty"` // File patterns to include (e.g., "*.yaml", "*.json")
    ExcludePatterns []string `json:"excludePatterns,omitempty"` // File patterns to exclude

    // Git-specific configuration
    GitConfig *GitSourceConfig `json:"gitConfig,omitempty"`

    // API-specific configuration
    ApiConfig *ApiSourceConfig `json:"apiConfig,omitempty"`

    // File-specific configuration
    FileConfig *FileSourceConfig `json:"fileConfig,omitempty"`
}

// GitSourceConfig contains Git-specific configuration
type GitSourceConfig struct {
    RepoURL     string `json:"repoURL"`               // Git repository URL
    Branch      string `json:"branch"`                // Git branch to scan (default: main/master)
    AuthType    string `json:"authType"`              // Auth type (token, basic, none)
    Token       string `json:"token,omitempty"`       // Git token
    Username    string `json:"username,omitempty"`    // Git username (for basic auth)
    Password    string `json:"password,omitempty"`    // Git password (for basic auth)
    ClonePath   string `json:"clonePath,omitempty"`   // Local clone path (auto-generated)
}

// ApiSourceConfig contains API-specific configuration
type ApiSourceConfig struct {
    Endpoint        string            `json:"endpoint"`                  // API endpoint URL
    Method          string            `json:"method"`                    // HTTP method (GET, POST, etc.)
    Headers         map[string]string `json:"headers,omitempty"`         // HTTP headers
    BodyTemplate    string            `json:"bodyTemplate,omitempty"`   // Request body template
    AuthType        string            `json:"authType"`                  // Auth type (none, basic, bearer, api-key)
    Username        string            `json:"username,omitempty"`        // API username
    Password        string            `json:"password,omitempty"`        // API password
    Token           string            `json:"token,omitempty"`           // API token
    ApiKey          string            `json:"apiKey,omitempty"`          // API key
    ApiKeyHeader    string            `json:"apiKeyHeader,omitempty"`    // API key header name
    ResponsePath    string            `json:"responsePath,omitempty"`    // JSON path to extract data from response
}

// FileSourceConfig contains file-specific configuration
type FileSourceConfig struct {
    BasePath    string   `json:"basePath"`              // Base directory path
    Recursive   bool     `json:"recursive"`             // Whether to scan recursively
    FileTypes   []string `json:"fileTypes,omitempty"`   // File extensions to scan
}

// UsageSourceList represents a list of usage sources
type UsageSourceList struct {
    Sources []UsageSource `json:"sources"`
}
```

#### UsageResult

```go
// UsageResult represents a detected usage of a group
type UsageResult struct {
    ID          string    `json:"id"`          // Unique identifier
    GroupName   string    `json:"groupName"`   // Name of the group
    SourceID    string    `json:"sourceId"`    // ID of the source where usage was found
    SourceName  string    `json:"sourceName"`  // Name of the source
    SourceType  SourceType `json:"sourceType"` // Type of source (git, api, file)
    FilePath    string    `json:"filePath"`    // Path to file (if applicable)
    LineNumber  int       `json:"lineNumber"`  // Line number (if applicable)
    Context     string    `json:"context"`     // Context around the usage (surrounding text)
    MatchType   string    `json:"matchType"`   // Type of match (exact, partial, regex)
    DetectedAt  time.Time `json:"detectedAt"`  // When the usage was detected
    RepoID      string    `json:"repoId"`      // ID of the repository the group belongs to

    // Additional metadata
    FileSize    int64  `json:"fileSize,omitempty"`    // Size of the file where usage was found
    FileType    string `json:"fileType,omitempty"`    // Type/extension of the file
    CommitHash  string `json:"commitHash,omitempty"`  // Git commit hash (for git sources)
    Branch      string `json:"branch,omitempty"`      // Git branch (for git sources)
    ApiResponse string `json:"apiResponse,omitempty"` // API response snippet (for api sources)
}

// UsageResultList represents a list of usage results
type UsageResultList struct {
    Results []UsageResult `json:"results"`
    Total   int           `json:"total"`   // Total number of results
    Page    int           `json:"page"`    // Current page (for pagination)
    PageSize int          `json:"pageSize"` // Page size
}

// UsageScanStatus represents the status of a usage scan
type UsageScanStatus struct {
    GroupName        string              `json:"groupName"`        // Name of the group
    SourcesTotal     int                 `json:"sourcesTotal"`     // Total number of sources
    SourcesScanned   int                 `json:"sourcesScanned"`   // Number of sources scanned
    InProgress       bool                `json:"inProgress"`       // Whether scan is in progress
    LastScanTime     time.Time           `json:"lastScanTime"`     // Last scan time
    CompletedSources []string            `json:"completedSources"` // IDs of completed sources
    PendingSources   []string            `json:"pendingSources"`   // IDs of pending sources
    FailedSources    []SourceScanFailure `json:"failedSources"`    // Sources that failed to scan
    RepoID           string              `json:"repoId"`           // ID of the repository
    TotalUsages      int                 `json:"totalUsages"`      // Total usages found
    ScanDuration     time.Duration       `json:"scanDuration"`     // Duration of the scan
}

// SourceScanFailure represents a failed source scan
type SourceScanFailure struct {
    SourceID    string `json:"sourceId"`    // ID of the failed source
    SourceName  string `json:"sourceName"`  // Name of the failed source
    Error       string `json:"error"`       // Error message
    FailedAt    time.Time `json:"failedAt"` // When the failure occurred
}

// UsageScanRequest represents a request to scan for group usage
type UsageScanRequest struct {
    GroupName string   `json:"groupName"`           // Name of the group to scan for
    SourceIDs []string `json:"sourceIds,omitempty"` // Specific source IDs to scan (empty = all active sources)
    RepoID    string   `json:"repoId"`              // ID of the repository
    Force     bool     `json:"force"`               // Force rescan even if recently scanned
}
```

### Interfaces

#### SourceHandler Interface

```go
// SourceHandler defines the interface for different source type handlers
type SourceHandler interface {
    // Initialize the handler with source configuration
    Initialize(source UsageSource) error

    // Scan for group usage in this source
    ScanForGroupUsage(ctx context.Context, groupName string) ([]UsageResult, error)

    // Get source type that this handler supports
    GetSourceType() SourceType

    // Validate source configuration
    ValidateConfig(source UsageSource) error

    // Test connection to the source
    TestConnection(ctx context.Context) error

    // Get source status (available, error, etc.)
    GetStatus(ctx context.Context) (SourceStatus, error)

    // Cleanup resources
    Cleanup() error
}

// SourceStatus represents the status of a source
type SourceStatus struct {
    Available   bool      `json:"available"`   // Whether the source is available
    LastChecked time.Time `json:"lastChecked"` // When the status was last checked
    Error       string    `json:"error"`       // Error message if not available
    Metadata    map[string]interface{} `json:"metadata,omitempty"` // Additional metadata
}
```

#### UsageSourceManager Interface

```go
// UsageSourceManager defines the interface for managing usage sources
type UsageSourceManager interface {
    // CRUD operations
    CreateSource(source UsageSource) (UsageSource, error)
    GetSource(id string) (UsageSource, error)
    GetAllSources() ([]UsageSource, error)
    GetActiveSourcesByType(sourceType SourceType) ([]UsageSource, error)
    UpdateSource(source UsageSource) (UsageSource, error)
    DeleteSource(id string) error

    // Configuration management
    LoadSources() error
    SaveSources() error

    // Source validation
    ValidateSource(source UsageSource) error
    TestSourceConnection(id string) error
}
```

#### UsageScanner Interface

```go
// UsageScanner defines the interface for scanning sources for group usage
type UsageScanner interface {
    // Scan for group usage across all active sources
    ScanGroupUsage(ctx context.Context, request UsageScanRequest) error

    // Get scan status for a group
    GetScanStatus(groupName, repoID string) (UsageScanStatus, error)

    // Get usage results for a group
    GetUsageResults(groupName, repoID string, page, pageSize int) (UsageResultList, error)

    // Cancel ongoing scan
    CancelScan(groupName, repoID string) error

    // Clear usage results for a group
    ClearUsageResults(groupName, repoID string) error

    // Get usage statistics
    GetUsageStatistics(repoID string) (UsageStatistics, error)
}

// UsageStatistics represents usage statistics
type UsageStatistics struct {
    TotalGroups        int `json:"totalGroups"`        // Total number of groups
    GroupsWithUsage    int `json:"groupsWithUsage"`    // Groups with detected usage
    TotalUsages        int `json:"totalUsages"`        // Total usage occurrences
    SourcesConfigured  int `json:"sourcesConfigured"`  // Number of configured sources
    SourcesActive      int `json:"sourcesActive"`      // Number of active sources
    LastScanTime       time.Time `json:"lastScanTime"` // Last scan time
}
```

### Services

#### UsageSourceManager

Responsible for managing usage sources (CRUD operations, storage).

#### UsageScanner

Responsible for scanning sources for group usage and storing results.

#### Source Handlers

Implementations of the SourceHandler interface for different source types:
- GitSourceHandler
- ApiSourceHandler
- FileSourceHandler

### API Endpoints

#### Usage Sources Management

- `GET /api/usage-sources` - List all usage sources with optional filtering
  - Query params: `type`, `active`, `page`, `pageSize`
  - Response: `{sources: UsageSource[], total: number, page: number, pageSize: number}`

- `GET /api/usage-sources/:id` - Get a specific usage source
  - Response: `UsageSource`

- `POST /api/usage-sources` - Create a new usage source
  - Body: `UsageSource` (without ID, timestamps)
  - Response: `UsageSource` (with generated ID and timestamps)

- `PUT /api/usage-sources/:id` - Update a usage source
  - Body: `UsageSource`
  - Response: `UsageSource`

- `DELETE /api/usage-sources/:id` - Delete a usage source
  - Response: `{success: boolean, message: string}`

- `POST /api/usage-sources/:id/test` - Test connection to a usage source
  - Response: `{success: boolean, status: SourceStatus}`

#### Group Usage Tracking

- `GET /api/repo/:repoId/groups/:groupName/usage` - Get usage results for a group
  - Query params: `page`, `pageSize`, `sourceType`, `sourceId`
  - Response: `UsageResultList`

- `POST /api/repo/:repoId/groups/:groupName/scan` - Trigger a scan for a group
  - Body: `UsageScanRequest`
  - Response: `{success: boolean, message: string, scanId: string}`

- `GET /api/repo/:repoId/groups/:groupName/scan-status` - Get scan status for a group
  - Response: `UsageScanStatus`

- `DELETE /api/repo/:repoId/groups/:groupName/scan` - Cancel ongoing scan
  - Response: `{success: boolean, message: string}`

- `DELETE /api/repo/:repoId/groups/:groupName/usage` - Clear usage results for a group
  - Response: `{success: boolean, message: string}`

#### Usage Statistics

- `GET /api/repo/:repoId/usage-statistics` - Get usage statistics for a repository
  - Response: `UsageStatistics`

- `GET /api/usage-sources/statistics` - Get overall usage source statistics
  - Response: `{totalSources: number, activeSources: number, sourcesByType: object}`

### Frontend Components

#### Settings Page

- UsageSourcesList - List of configured sources
- UsageSourceForm - Form for adding/editing sources
- SourceTypeSelector - Dropdown for selecting source type
- DynamicSourceForm - Dynamic form based on source type

#### Group Detail Page

- UsageTab - New tab in group detail page
- UsageResultsList - List of usage results grouped by source
- UsageStatusIndicator - Shows scan status
- ManualScanButton - Button to trigger manual scan

## Storage Design

### File Structure

```
data/
├── usage-sources/
│   └── sources.json              # Usage source configurations
├── usage-results/
│   ├── {repoId}/
│   │   ├── {groupName}/
│   │   │   ├── results.json      # Usage results for the group
│   │   │   └── scan-status.json  # Scan status for the group
│   │   └── statistics.json       # Repository usage statistics
│   └── global-stats.json         # Global usage statistics
└── usage-cache/
    └── {sourceId}/
        └── {timestamp}/           # Cached source data for performance
```

### Configuration Files

- `configs/usage-sources.json` - Main configuration file for usage sources
- `data/usage-sources/` - Additional source-specific data and cache

## Implementation Plan

### Phase 1: Backend Foundation
1. Create usage tracking models (`src/backend/models/usage.go`)
2. Implement usage source manager service
3. Create basic file-based storage
4. Write comprehensive unit tests

### Phase 2: Source Handlers
1. Implement SourceHandler interface
2. Create GitSourceHandler implementation
3. Create ApiSourceHandler implementation
4. Create FileSourceHandler implementation
5. Add handler registration system

### Phase 3: Background Processing
1. Extend scheduler service for usage scanning
2. Implement UsageScanner service
3. Add scan status tracking
4. Implement manual scan triggers

### Phase 4: API Layer
1. Create UsageSourceController
2. Create UsageResultController
3. Add API endpoints following existing patterns
4. Write integration tests

### Phase 5: Frontend Implementation
1. Create usage source management components
2. Add Usage tab to group detail page
3. Implement usage display components
4. Add manual scan triggers

### Phase 6: Integration and Testing
1. End-to-end testing
2. Performance optimization
3. Documentation
4. Final integration

## Testing Strategy

### Backend Testing
1. **Unit Tests**: All models, services, and handlers
2. **Integration Tests**: API endpoints with mock data
3. **Mock Implementations**: External dependencies (Git, APIs)
4. **Performance Tests**: Large dataset scanning

### Frontend Testing
1. **Component Tests**: React components with React Testing Library
2. **Integration Tests**: Component interactions
3. **E2E Tests**: Complete workflows with Cypress

### Test Data
- Mock Git repositories with various file types
- Sample API responses
- Test group configurations
- Performance test datasets

## Security Considerations

1. **Credential Storage**: Encrypt sensitive data (tokens, passwords)
2. **Access Control**: Validate user permissions for source management
3. **Input Validation**: Sanitize all user inputs
4. **Rate Limiting**: Prevent abuse of scan triggers
5. **Audit Logging**: Log all configuration changes and scan activities

## Performance Considerations

1. **Caching**: Cache source data to reduce external calls
2. **Parallel Processing**: Scan multiple sources concurrently
3. **Incremental Scanning**: Only scan changed files in Git sources
4. **Result Pagination**: Paginate large result sets
5. **Background Processing**: Non-blocking scan operations

## Error Handling

1. **Source Connectivity**: Handle network failures gracefully
2. **Authentication**: Clear error messages for auth failures
3. **Rate Limiting**: Handle API rate limits with backoff
4. **Data Corruption**: Validate and recover from corrupted data
5. **Resource Limits**: Handle memory and disk space constraints
