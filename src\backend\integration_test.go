package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"adgitops-ui/src/backend/api"
	"adgitops-ui/src/backend/controllers"
	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// IntegrationTestSuite contains the test setup
type IntegrationTestSuite struct {
	server          *api.Server
	dataDir         string
	usageController *controllers.UsageController
	sourceManager   services.UsageSourceManager
	scannerService  *services.UsageScannerService
	testServer      *httptest.Server
}

// setupIntegrationTest sets up the integration test environment
func setupIntegrationTest(t *testing.T) *IntegrationTestSuite {
	// Create temporary directory for test data
	tempDir, err := ioutil.TempDir("", "adgitops_integration_test")
	require.NoError(t, err)

	// Initialize services
	sourceManager := services.NewUsageSourceManager(tempDir)
	scannerService := services.NewUsageScannerService(sourceManager, tempDir)

	// Initialize controllers
	usageController := controllers.NewUsageController(sourceManager, scannerService)

	// Create API server
	server := api.NewServer("8080")
	server.AddController(usageController)

	// Create test server
	testServer := httptest.NewServer(server.GetRouter())

	return &IntegrationTestSuite{
		server:          server,
		dataDir:         tempDir,
		usageController: usageController,
		sourceManager:   sourceManager,
		scannerService:  scannerService,
		testServer:      testServer,
	}
}

// teardownIntegrationTest cleans up the test environment
func (suite *IntegrationTestSuite) teardownIntegrationTest() {
	if suite.testServer != nil {
		suite.testServer.Close()
	}
	if suite.dataDir != "" {
		os.RemoveAll(suite.dataDir)
	}
}

// makeRequest makes an HTTP request to the test server
func (suite *IntegrationTestSuite) makeRequest(method, path string, body interface{}) (*http.Response, []byte, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, nil, err
		}
	}

	req, err := http.NewRequest(method, suite.testServer.URL+path, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, nil, err
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return resp, nil, err
	}

	return resp, respBody, nil
}

// TestUsageTrackingWorkflow tests the complete usage tracking workflow
func TestUsageTrackingWorkflow(t *testing.T) {
	suite := setupIntegrationTest(t)
	defer suite.teardownIntegrationTest()

	// Start the scanner service
	err := suite.scannerService.Start()
	require.NoError(t, err)
	defer suite.scannerService.Stop()

	// Step 1: Create a usage source
	t.Run("CreateUsageSource", func(t *testing.T) {
		source := models.UsageSource{
			Name:          "Test Integration Source",
			Type:          models.SourceTypeGit,
			IsActive:      true,
			ScanFrequency: 300, // 5 minutes
			GitConfig: &models.GitSourceConfig{
				RepoURL:  "https://github.com/test/repo.git",
				Branch:   "main",
				AuthType: "none",
			},
		}

		resp, body, err := suite.makeRequest("POST", "/api/usage-sources", source)
		require.NoError(t, err)
		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var createdSource models.UsageSource
		err = json.Unmarshal(body, &createdSource)
		require.NoError(t, err)

		assert.NotEmpty(t, createdSource.ID)
		assert.Equal(t, source.Name, createdSource.Name)
		assert.Equal(t, source.Type, createdSource.Type)
		assert.Equal(t, source.IsActive, createdSource.IsActive)
	})

	// Step 2: Get all usage sources
	t.Run("GetUsageSources", func(t *testing.T) {
		resp, body, err := suite.makeRequest("GET", "/api/usage-sources", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		require.NoError(t, err)

		sources := response["sources"].([]interface{})
		assert.Len(t, sources, 1)
		assert.Equal(t, float64(1), response["total"])
	})

	// Step 3: Get usage source statistics
	t.Run("GetUsageSourceStatistics", func(t *testing.T) {
		resp, body, err := suite.makeRequest("GET", "/api/usage-sources/statistics", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var stats map[string]interface{}
		err = json.Unmarshal(body, &stats)
		require.NoError(t, err)

		assert.Equal(t, float64(1), stats["totalSources"])
		assert.Equal(t, float64(1), stats["activeSources"])
	})

	// Step 4: Trigger a group usage scan
	t.Run("TriggerGroupScan", func(t *testing.T) {
		scanRequest := map[string]interface{}{
			"force": true,
		}

		resp, body, err := suite.makeRequest("POST", "/api/repo/test-repo/groups/test-group/scan", scanRequest)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		require.NoError(t, err)

		assert.True(t, response["success"].(bool))
		assert.Contains(t, response["message"], "started successfully")
	})

	// Step 5: Check scan status
	t.Run("CheckScanStatus", func(t *testing.T) {
		// Wait a bit for scan to start
		time.Sleep(100 * time.Millisecond)

		resp, body, err := suite.makeRequest("GET", "/api/repo/test-repo/groups/test-group/scan-status", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var status models.UsageScanStatus
		err = json.Unmarshal(body, &status)
		require.NoError(t, err)

		assert.Equal(t, "test-group", status.GroupName)
		assert.Equal(t, "test-repo", status.RepoID)
		assert.True(t, status.SourcesTotal > 0)
	})

	// Step 6: Wait for scan to complete and get results
	t.Run("GetUsageResults", func(t *testing.T) {
		// Wait for scan to complete (with timeout)
		timeout := time.After(10 * time.Second)
		ticker := time.NewTicker(500 * time.Millisecond)
		defer ticker.Stop()

		var scanCompleted bool
		for !scanCompleted {
			select {
			case <-timeout:
				t.Log("Timeout waiting for scan to complete")
				return
			case <-ticker.C:
				resp, body, err := suite.makeRequest("GET", "/api/repo/test-repo/groups/test-group/scan-status", nil)
				if err != nil {
					continue
				}
				if resp.StatusCode != http.StatusOK {
					continue
				}

				var status models.UsageScanStatus
				if err := json.Unmarshal(body, &status); err != nil {
					continue
				}

				if !status.InProgress {
					scanCompleted = true
					t.Log("Scan completed")
				}
			}
		}

		// Get usage results
		resp, body, err := suite.makeRequest("GET", "/api/repo/test-repo/groups/test-group/usage", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var results models.UsageResultList
		err = json.Unmarshal(body, &results)
		require.NoError(t, err)

		// Results might be empty if the test Git repo doesn't exist, but the structure should be correct
		assert.NotNil(t, results.Results)
		assert.Equal(t, 1, results.Page)
		assert.Equal(t, 50, results.PageSize)
	})

	// Step 7: Get repository usage statistics
	t.Run("GetRepoUsageStatistics", func(t *testing.T) {
		resp, body, err := suite.makeRequest("GET", "/api/repo/test-repo/usage-statistics", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var stats models.UsageStatistics
		err = json.Unmarshal(body, &stats)
		require.NoError(t, err)

		// Basic structure validation
		assert.True(t, stats.SourcesConfigured >= 0)
		assert.True(t, stats.SourcesActive >= 0)
	})

	// Step 8: Clear usage results
	t.Run("ClearUsageResults", func(t *testing.T) {
		resp, body, err := suite.makeRequest("DELETE", "/api/repo/test-repo/groups/test-group/usage", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		require.NoError(t, err)

		assert.True(t, response["success"].(bool))
		assert.Contains(t, response["message"], "cleared successfully")
	})
}

// TestUsageSourceCRUD tests the CRUD operations for usage sources
func TestUsageSourceCRUD(t *testing.T) {
	suite := setupIntegrationTest(t)
	defer suite.teardownIntegrationTest()

	var sourceID string

	// Create
	t.Run("Create", func(t *testing.T) {
		source := models.UsageSource{
			Name:          "CRUD Test Source",
			Type:          models.SourceTypeAPI,
			IsActive:      true,
			ScanFrequency: 600,
			ApiConfig: &models.ApiSourceConfig{
				Endpoint: "https://api.example.com/data",
				Method:   "GET",
				AuthType: "none",
			},
		}

		resp, body, err := suite.makeRequest("POST", "/api/usage-sources", source)
		require.NoError(t, err)
		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var createdSource models.UsageSource
		err = json.Unmarshal(body, &createdSource)
		require.NoError(t, err)

		sourceID = createdSource.ID
		assert.NotEmpty(t, sourceID)
		assert.Equal(t, source.Name, createdSource.Name)
	})

	// Read
	t.Run("Read", func(t *testing.T) {
		resp, body, err := suite.makeRequest("GET", fmt.Sprintf("/api/usage-sources/%s", sourceID), nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var source models.UsageSource
		err = json.Unmarshal(body, &source)
		require.NoError(t, err)

		assert.Equal(t, sourceID, source.ID)
		assert.Equal(t, "CRUD Test Source", source.Name)
	})

	// Update
	t.Run("Update", func(t *testing.T) {
		updatedSource := models.UsageSource{
			ID:            sourceID,
			Name:          "Updated CRUD Test Source",
			Type:          models.SourceTypeAPI,
			IsActive:      false,
			ScanFrequency: 1200,
			ApiConfig: &models.ApiSourceConfig{
				Endpoint: "https://api.example.com/updated",
				Method:   "POST",
				AuthType: "basic",
				Username: "testuser",
				Password: "testpass",
			},
		}

		resp, body, err := suite.makeRequest("PUT", fmt.Sprintf("/api/usage-sources/%s", sourceID), updatedSource)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var source models.UsageSource
		err = json.Unmarshal(body, &source)
		require.NoError(t, err)

		assert.Equal(t, "Updated CRUD Test Source", source.Name)
		assert.False(t, source.IsActive)
		assert.Equal(t, 1200, source.ScanFrequency)
	})

	// Delete
	t.Run("Delete", func(t *testing.T) {
		resp, body, err := suite.makeRequest("DELETE", fmt.Sprintf("/api/usage-sources/%s", sourceID), nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		require.NoError(t, err)

		assert.True(t, response["success"].(bool))

		// Verify deletion
		resp, _, err = suite.makeRequest("GET", fmt.Sprintf("/api/usage-sources/%s", sourceID), nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}

// TestErrorHandling tests error handling scenarios
func TestErrorHandling(t *testing.T) {
	suite := setupIntegrationTest(t)
	defer suite.teardownIntegrationTest()

	// Test invalid source creation
	t.Run("InvalidSourceCreation", func(t *testing.T) {
		invalidSource := models.UsageSource{
			Name: "", // Empty name should cause validation error
			Type: models.SourceTypeGit,
		}

		resp, body, err := suite.makeRequest("POST", "/api/usage-sources", invalidSource)
		require.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, resp.StatusCode)

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		require.NoError(t, err)

		assert.Contains(t, response["error"], "validation")
	})

	// Test non-existent source retrieval
	t.Run("NonExistentSource", func(t *testing.T) {
		resp, _, err := suite.makeRequest("GET", "/api/usage-sources/non-existent-id", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	// Test scan status for non-existent group
	t.Run("NonExistentGroupScanStatus", func(t *testing.T) {
		resp, _, err := suite.makeRequest("GET", "/api/repo/test-repo/groups/non-existent-group/scan-status", nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}
