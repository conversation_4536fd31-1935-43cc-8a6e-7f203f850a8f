package controllers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/gin-gonic/gin"
)

// UsageController handles usage tracking API endpoints
type UsageController struct {
	sourceManager services.UsageSourceManager
	scanner       services.UsageScanner
	scanLogger    *services.ScanLogger
}

// NewUsageController creates a new usage controller
func NewUsageController(sourceManager services.UsageSourceManager, scanner services.UsageScanner, scanLogger *services.ScanLogger) *UsageController {
	return &UsageController{
		sourceManager: sourceManager,
		scanner:       scanner,
		scanLogger:    scanLogger,
	}
}

// RegisterRoutes registers the usage tracking routes
func (c *UsageController) RegisterRoutes(router *gin.RouterGroup) {
	// Usage Sources Management
	usageSources := router.Group("/usage-sources")
	{
		usageSources.GET("", c.GetUsageSources)
		usageSources.GET("/:id", c.GetUsageSource)
		usageSources.POST("", c.CreateUsageSource)
		usageSources.PUT("/:id", c.UpdateUsageSource)
		usageSources.DELETE("/:id", c.DeleteUsageSource)
		usageSources.POST("/:id/test", c.TestUsageSource)
		usageSources.GET("/statistics", c.GetUsageSourceStatistics)
	}

	// Group Usage Tracking - use different base path to avoid conflicts
	usageRepos := router.Group("/usage-repo/:repoId")
	{
		groups := usageRepos.Group("/groups/:groupName")
		{
			groups.GET("/usage", c.GetGroupUsage)
			groups.POST("/scan", c.ScanGroupUsage)
			groups.GET("/scan-status", c.GetGroupScanStatus)
			groups.DELETE("/scan", c.CancelGroupScan)
			groups.DELETE("/usage", c.ClearGroupUsage)
		}
		usageRepos.GET("/usage-statistics", c.GetRepoUsageStatistics)
	}

	// Scan Logs Management
	scanLogs := router.Group("/scan-logs")
	{
		scanLogs.GET("", c.GetScanLogs)
		scanLogs.GET("/overview", c.GetScanLogsOverview)
		scanLogs.GET("/summaries", c.GetScanSummaries)
		scanLogs.GET("/:scanId", c.GetScanLogsByScanId)
		scanLogs.DELETE("/:scanId", c.ClearScanLogs)
	}

	// Repository-specific scan logs - use different path to avoid conflicts
	repoScanLogs := router.Group("/usage-repo/:repoId/scan-logs")
	{
		repoScanLogs.GET("", c.GetRepoScanLogs)
		repoScanLogs.GET("/groups/:groupName", c.GetGroupScanLogs)
	}
}

// GetUsageSources retrieves all usage sources with optional filtering
func (c *UsageController) GetUsageSources(ctx *gin.Context) {
	// Parse query parameters
	sourceType := ctx.Query("type")
	activeStr := ctx.Query("active")
	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("pageSize", "50")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	// Get all sources
	allSources, err := c.sourceManager.GetAllSources()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve usage sources"})
		return
	}

	// Apply filters
	var filteredSources []models.UsageSource
	for _, source := range allSources {
		// Filter by type
		if sourceType != "" && string(source.Type) != sourceType {
			continue
		}

		// Filter by active status
		if activeStr != "" {
			active, err := strconv.ParseBool(activeStr)
			if err == nil && source.IsActive != active {
				continue
			}
		}

		filteredSources = append(filteredSources, source)
	}

	// Apply pagination
	total := len(filteredSources)
	start := (page - 1) * pageSize
	if start >= total {
		filteredSources = []models.UsageSource{}
	} else {
		end := start + pageSize
		if end > total {
			end = total
		}
		filteredSources = filteredSources[start:end]
	}

	ctx.JSON(http.StatusOK, gin.H{
		"sources":  filteredSources,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// GetUsageSource retrieves a specific usage source
func (c *UsageController) GetUsageSource(ctx *gin.Context) {
	id := ctx.Param("id")

	source, err := c.sourceManager.GetSource(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Usage source not found"})
		return
	}

	ctx.JSON(http.StatusOK, source)
}

// CreateUsageSource creates a new usage source
func (c *UsageController) CreateUsageSource(ctx *gin.Context) {
	var source models.UsageSource
	if err := ctx.ShouldBindJSON(&source); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	createdSource, err := c.sourceManager.CreateSource(source)
	if err != nil {
		if validationErr, ok := err.(*models.ValidationError); ok {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": validationErr.Message, "field": validationErr.Field})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create usage source"})
		return
	}

	ctx.JSON(http.StatusCreated, createdSource)
}

// UpdateUsageSource updates an existing usage source
func (c *UsageController) UpdateUsageSource(ctx *gin.Context) {
	id := ctx.Param("id")

	var source models.UsageSource
	if err := ctx.ShouldBindJSON(&source); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Ensure the ID matches
	source.ID = id

	updatedSource, err := c.sourceManager.UpdateSource(source)
	if err != nil {
		if validationErr, ok := err.(*models.ValidationError); ok {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": validationErr.Message, "field": validationErr.Field})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update usage source"})
		return
	}

	ctx.JSON(http.StatusOK, updatedSource)
}

// DeleteUsageSource deletes a usage source
func (c *UsageController) DeleteUsageSource(ctx *gin.Context) {
	id := ctx.Param("id")

	if err := c.sourceManager.DeleteSource(id); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete usage source"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Usage source deleted successfully"})
}

// TestUsageSource tests the connection to a usage source
func (c *UsageController) TestUsageSource(ctx *gin.Context) {
	id := ctx.Param("id")

	if err := c.sourceManager.TestSourceConnection(id); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"success": false,
			"status": models.SourceStatus{
				Available:   false,
				LastChecked: time.Now(),
				Error:       err.Error(),
			},
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"status": models.SourceStatus{
			Available:   true,
			LastChecked: time.Now(),
			Error:       "",
		},
	})
}

// GetUsageSourceStatistics gets overall usage source statistics
func (c *UsageController) GetUsageSourceStatistics(ctx *gin.Context) {
	sources, err := c.sourceManager.GetAllSources()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve sources"})
		return
	}

	totalSources := len(sources)
	activeSources := 0
	sourcesByType := make(map[string]int)

	for _, source := range sources {
		if source.IsActive {
			activeSources++
		}
		sourcesByType[string(source.Type)]++
	}

	ctx.JSON(http.StatusOK, gin.H{
		"totalSources":  totalSources,
		"activeSources": activeSources,
		"sourcesByType": sourcesByType,
	})
}

// GetGroupUsage gets usage results for a group
func (c *UsageController) GetGroupUsage(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	groupName := ctx.Param("groupName")

	// Parse query parameters
	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("pageSize", "50")
	sourceType := ctx.Query("sourceType")
	sourceID := ctx.Query("sourceId")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	// Get usage results
	results, err := c.scanner.GetUsageResults(groupName, repoID, page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve usage results"})
		return
	}

	// Apply additional filters if specified
	if sourceType != "" || sourceID != "" {
		var filteredResults []models.UsageResult
		for _, result := range results.Results {
			if sourceType != "" && string(result.SourceType) != sourceType {
				continue
			}
			if sourceID != "" && result.SourceID != sourceID {
				continue
			}
			filteredResults = append(filteredResults, result)
		}
		results.Results = filteredResults
		results.Total = len(filteredResults)
	}

	ctx.JSON(http.StatusOK, results)
}

// ScanGroupUsage triggers a scan for a group
func (c *UsageController) ScanGroupUsage(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	groupName := ctx.Param("groupName")

	var request models.UsageScanRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Set the group name and repo ID from URL parameters
	request.GroupName = groupName
	request.RepoID = repoID

	// Check if a scan is already in progress (unless force is specified)
	if !request.Force {
		status, err := c.scanner.GetScanStatus(groupName, repoID)
		if err == nil && status.InProgress {
			ctx.JSON(http.StatusConflict, gin.H{
				"error":      "Scan already in progress for this group",
				"message":    fmt.Sprintf("A scan for group '%s' in repository '%s' is already running. Use 'force: true' to override.", groupName, repoID),
				"inProgress": true,
				"startTime":  status.LastScanTime,
			})
			return
		}
	}

	// Start the scan
	scanCtx := context.Background()
	if err := c.scanner.ScanGroupUsage(scanCtx, request); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to start scan",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Scan started successfully",
		"scanId":  groupName + ":" + repoID,
	})
}

// GetGroupScanStatus gets scan status for a group
func (c *UsageController) GetGroupScanStatus(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	groupName := ctx.Param("groupName")

	status, err := c.scanner.GetScanStatus(groupName, repoID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Scan status not found"})
		return
	}

	ctx.JSON(http.StatusOK, status)
}

// CancelGroupScan cancels an ongoing scan
func (c *UsageController) CancelGroupScan(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	groupName := ctx.Param("groupName")

	// Check if scan is actually running
	status, err := c.scanner.GetScanStatus(groupName, repoID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error":   "Scan status not found",
			"details": err.Error(),
		})
		return
	}

	if !status.InProgress {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "No scan in progress",
			"message": fmt.Sprintf("No active scan found for group '%s' in repository '%s'", groupName, repoID),
		})
		return
	}

	// Cancel the scan
	if err := c.scanner.CancelScan(groupName, repoID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to cancel scan",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":     true,
		"message":     fmt.Sprintf("Scan cancelled successfully for group '%s' in repository '%s'", groupName, repoID),
		"cancelledAt": time.Now(),
	})
}

// ClearGroupUsage clears usage results for a group
func (c *UsageController) ClearGroupUsage(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	groupName := ctx.Param("groupName")

	if err := c.scanner.ClearUsageResults(groupName, repoID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear usage results"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Usage results cleared successfully"})
}

// GetRepoUsageStatistics gets usage statistics for a repository
func (c *UsageController) GetRepoUsageStatistics(ctx *gin.Context) {
	repoID := ctx.Param("repoId")

	stats, err := c.scanner.GetUsageStatistics(repoID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve usage statistics"})
		return
	}

	ctx.JSON(http.StatusOK, stats)
}

// GetScanLogs retrieves scan logs with filtering and pagination
func (c *UsageController) GetScanLogs(ctx *gin.Context) {
	var filter models.ScanLogFilter

	// Parse query parameters
	if repoId := ctx.Query("repoId"); repoId != "" {
		filter.RepoID = repoId
	}
	if groupName := ctx.Query("groupName"); groupName != "" {
		filter.GroupName = groupName
	}
	if scanId := ctx.Query("scanId"); scanId != "" {
		filter.ScanID = scanId
	}
	if level := ctx.Query("level"); level != "" {
		filter.Level = models.LogLevel(level)
	}
	if status := ctx.Query("status"); status != "" {
		filter.Status = status
	}
	if sourceId := ctx.Query("sourceId"); sourceId != "" {
		filter.SourceID = sourceId
	}
	if searchQuery := ctx.Query("search"); searchQuery != "" {
		filter.SearchQuery = searchQuery
	}

	// Parse pagination parameters
	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	filter.Page = page

	pageSize := 50
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 200 {
			pageSize = ps
		}
	}
	filter.PageSize = pageSize

	// Parse time range filters
	if startTimeStr := ctx.Query("startTime"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filter.StartTime = &startTime
		}
	}
	if endTimeStr := ctx.Query("endTime"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filter.EndTime = &endTime
		}
	}

	// Get logs from scan logger
	response := c.scanLogger.GetScanLogs(filter)

	ctx.JSON(http.StatusOK, response)
}

// GetScanLogsOverview provides an overview of all scan activities
func (c *UsageController) GetScanLogsOverview(ctx *gin.Context) {
	overview := c.scanLogger.GetScanOverview()
	ctx.JSON(http.StatusOK, overview)
}

// GetScanSummaries retrieves scan summaries with filtering
func (c *UsageController) GetScanSummaries(ctx *gin.Context) {
	var filter models.ScanLogFilter

	// Parse query parameters
	if repoId := ctx.Query("repoId"); repoId != "" {
		filter.RepoID = repoId
	}
	if groupName := ctx.Query("groupName"); groupName != "" {
		filter.GroupName = groupName
	}
	if status := ctx.Query("status"); status != "" {
		filter.Status = status
	}

	// Parse time range filters
	if startTimeStr := ctx.Query("startTime"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filter.StartTime = &startTime
		}
	}
	if endTimeStr := ctx.Query("endTime"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filter.EndTime = &endTime
		}
	}

	summaries := c.scanLogger.GetScanSummaries(filter)
	ctx.JSON(http.StatusOK, gin.H{
		"summaries": summaries,
		"total":     len(summaries),
	})
}

// GetScanLogsByScanId retrieves logs for a specific scan ID
func (c *UsageController) GetScanLogsByScanId(ctx *gin.Context) {
	scanId := ctx.Param("scanId")
	if scanId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Scan ID is required"})
		return
	}

	filter := models.ScanLogFilter{
		ScanID:   scanId,
		Page:     1,
		PageSize: 1000, // Get all logs for this scan
	}

	response := c.scanLogger.GetScanLogs(filter)
	ctx.JSON(http.StatusOK, response)
}

// ClearScanLogs clears logs for a specific scan (placeholder for future implementation)
func (c *UsageController) ClearScanLogs(ctx *gin.Context) {
	scanId := ctx.Param("scanId")
	if scanId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Scan ID is required"})
		return
	}

	// For now, just return success - actual implementation would clear logs
	ctx.JSON(http.StatusOK, gin.H{
		"message": "Scan logs cleared successfully",
		"scanId":  scanId,
	})
}

// GetRepoScanLogs retrieves scan logs for a specific repository
func (c *UsageController) GetRepoScanLogs(ctx *gin.Context) {
	repoId := ctx.Param("repoId")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Repository ID is required"})
		return
	}

	var filter models.ScanLogFilter
	filter.RepoID = repoId

	// Parse other query parameters
	if groupName := ctx.Query("groupName"); groupName != "" {
		filter.GroupName = groupName
	}
	if level := ctx.Query("level"); level != "" {
		filter.Level = models.LogLevel(level)
	}
	if searchQuery := ctx.Query("search"); searchQuery != "" {
		filter.SearchQuery = searchQuery
	}

	// Parse pagination
	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	filter.Page = page

	pageSize := 50
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 200 {
			pageSize = ps
		}
	}
	filter.PageSize = pageSize

	response := c.scanLogger.GetScanLogs(filter)
	ctx.JSON(http.StatusOK, response)
}

// GetGroupScanLogs retrieves scan logs for a specific group
func (c *UsageController) GetGroupScanLogs(ctx *gin.Context) {
	repoId := ctx.Param("repoId")
	groupName := ctx.Param("groupName")

	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Repository ID is required"})
		return
	}
	if groupName == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Group name is required"})
		return
	}

	var filter models.ScanLogFilter
	filter.RepoID = repoId
	filter.GroupName = groupName

	// Parse other query parameters
	if level := ctx.Query("level"); level != "" {
		filter.Level = models.LogLevel(level)
	}
	if searchQuery := ctx.Query("search"); searchQuery != "" {
		filter.SearchQuery = searchQuery
	}

	// Parse pagination
	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	filter.Page = page

	pageSize := 50
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 200 {
			pageSize = ps
		}
	}
	filter.PageSize = pageSize

	response := c.scanLogger.GetScanLogs(filter)
	ctx.JSON(http.StatusOK, response)
}
