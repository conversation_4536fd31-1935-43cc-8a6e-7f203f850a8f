package services

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
)

// UsageScanner defines the interface for scanning sources for group usage
type UsageScanner interface {
	// Scan for group usage across all active sources
	ScanGroupUsage(ctx context.Context, request models.UsageScanRequest) error

	// Get scan status for a group
	GetScanStatus(groupName, repoID string) (models.UsageScanStatus, error)

	// Get usage results for a group
	GetUsageResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error)

	// Cancel ongoing scan
	CancelScan(groupName, repoID string) error

	// Clear usage results for a group
	ClearUsageResults(groupName, repoID string) error

	// Get usage statistics
	GetUsageStatistics(repoID string) (models.UsageStatistics, error)
}

// UsageScannerService manages usage scanning
type UsageScannerService struct {
	sourceManager      UsageSourceManager
	handlerRegistry    *SourceHandlerRegistry
	statusManager      *ScanStatusManager
	workerPool         *ScanWorkerPool
	scheduledScans     map[string]time.Time // Map of groupName:repoID to next scan time
	scheduleMutex      sync.RWMutex
	isRunning          bool
	ticker             *time.Ticker
	done               chan struct{}
	scanInterval       time.Duration // Default scan interval
	maxConcurrentScans int           // Maximum concurrent scans
	dataDir            string
}

// NewUsageScannerService creates a new usage scanner service
func NewUsageScannerService(sourceManager UsageSourceManager, dataDir string) *UsageScannerService {
	registry := NewSourceHandlerRegistry()
	statusManager := NewScanStatusManager(dataDir)
	workerPool := NewScanWorkerPool(5) // 5 concurrent workers

	// Set the status manager on the worker pool
	workerPool.SetStatusManager(statusManager)

	return &UsageScannerService{
		sourceManager:      sourceManager,
		handlerRegistry:    registry,
		statusManager:      statusManager,
		workerPool:         workerPool,
		scheduledScans:     make(map[string]time.Time),
		done:               make(chan struct{}),
		scanInterval:       time.Hour * 6, // Default: scan every 6 hours
		maxConcurrentScans: 10,
		dataDir:            dataDir,
	}
}

// Start begins the scanner service
func (s *UsageScannerService) Start() error {
	if s.isRunning {
		log.Println("Usage scanner service is already running")
		return nil
	}

	// Start worker pool
	s.workerPool.Start()

	// Check for scheduled scans every 5 minutes
	s.ticker = time.NewTicker(5 * time.Minute)
	s.isRunning = true

	go s.run()
	log.Println("Usage scanner service started")
	return nil
}

// Stop stops the scanner service
func (s *UsageScannerService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.done)
	s.ticker.Stop()
	s.workerPool.Stop()
	log.Println("Usage scanner service stopped")
}

// run is the main loop for the scanner
func (s *UsageScannerService) run() {
	// Run once immediately to catch up on any missed schedules
	s.checkSchedules()

	for {
		select {
		case <-s.done:
			return
		case <-s.ticker.C:
			if !s.isRunning {
				return
			}
			s.checkSchedules()
		}
	}
}

// checkSchedules checks for scheduled scans
func (s *UsageScannerService) checkSchedules() {
	// Get all active sources
	sources, err := s.sourceManager.GetAllSources()
	if err != nil {
		log.Printf("Error getting usage sources: %v", err)
		return
	}

	now := time.Now()

	for _, source := range sources {
		if !source.IsActive {
			continue
		}

		// Check if it's time to scan this source
		scanKey := fmt.Sprintf("source:%s", source.ID)
		s.scheduleMutex.RLock()
		nextScan, exists := s.scheduledScans[scanKey]
		s.scheduleMutex.RUnlock()

		if !exists {
			// First time scheduling this source
			nextScan = now.Add(time.Duration(source.ScanFrequency) * time.Second)
			s.scheduleMutex.Lock()
			s.scheduledScans[scanKey] = nextScan
			s.scheduleMutex.Unlock()
			log.Printf("Scheduled first scan for source %s at %s", source.Name, nextScan.Format(time.RFC3339))
			continue
		}

		if now.After(nextScan) || now.Equal(nextScan) {
			log.Printf("Triggering scheduled scan for source %s", source.Name)

			// TODO: Get all groups from all repositories and scan them
			// For now, we'll implement manual scanning only

			// Schedule next scan
			nextScan = now.Add(time.Duration(source.ScanFrequency) * time.Second)
			s.scheduleMutex.Lock()
			s.scheduledScans[scanKey] = nextScan
			s.scheduleMutex.Unlock()
		}
	}
}

// ScanGroupUsage initiates a scan for a group
func (s *UsageScannerService) ScanGroupUsage(ctx context.Context, request models.UsageScanRequest) error {
	log.Printf("Starting usage scan for group %s in repo %s", request.GroupName, request.RepoID)

	// Get sources to scan
	var sources []models.UsageSource
	var err error

	if len(request.SourceIDs) > 0 {
		// Scan specific sources
		for _, sourceID := range request.SourceIDs {
			source, err := s.sourceManager.GetSource(sourceID)
			if err != nil {
				log.Printf("Warning: Could not get source %s: %v", sourceID, err)
				continue
			}
			if source.IsActive {
				sources = append(sources, source)
			}
		}
	} else {
		// Scan all active sources
		sources, err = s.sourceManager.GetAllSources()
		if err != nil {
			return fmt.Errorf("failed to get sources: %w", err)
		}

		// Filter to active sources only
		var activeSources []models.UsageSource
		for _, source := range sources {
			if source.IsActive {
				activeSources = append(activeSources, source)
			}
		}
		sources = activeSources
	}

	if len(sources) == 0 {
		return fmt.Errorf("no active sources found to scan")
	}

	// Initialize scan status
	status := models.UsageScanStatus{
		GroupName:        request.GroupName,
		SourcesTotal:     len(sources),
		SourcesScanned:   0,
		InProgress:       true,
		LastScanTime:     time.Now(),
		CompletedSources: []string{},
		PendingSources:   make([]string, len(sources)),
		FailedSources:    []models.SourceScanFailure{},
		RepoID:           request.RepoID,
		TotalUsages:      0,
		ScanDuration:     0,
	}

	for i, source := range sources {
		status.PendingSources[i] = source.ID
	}

	// Save initial status
	if err := s.statusManager.UpdateStatus(status); err != nil {
		log.Printf("Warning: Failed to save initial scan status: %v", err)
	}

	// Submit scan jobs to worker pool
	for _, source := range sources {
		handler, err := s.handlerRegistry.GetHandler(source.Type)
		if err != nil {
			log.Printf("Warning: No handler for source type %s: %v", source.Type, err)
			continue
		}

		if err := handler.Initialize(source); err != nil {
			log.Printf("Warning: Failed to initialize handler for source %s: %v", source.Name, err)
			continue
		}

		job := ScanJob{
			GroupName: request.GroupName,
			RepoID:    request.RepoID,
			Source:    source,
			Handler:   handler,
			Context:   ctx,
		}

		if err := s.workerPool.SubmitJob(job); err != nil {
			log.Printf("Warning: Failed to submit scan job for source %s: %v", source.Name, err)
		}
	}

	return nil
}

// GetScanStatus gets the status of a scan
func (s *UsageScannerService) GetScanStatus(groupName, repoID string) (models.UsageScanStatus, error) {
	return s.statusManager.GetStatus(groupName, repoID)
}

// GetUsageResults gets the results of a scan
func (s *UsageScannerService) GetUsageResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error) {
	return s.statusManager.GetResults(groupName, repoID, page, pageSize)
}

// CancelScan cancels an ongoing scan
func (s *UsageScannerService) CancelScan(groupName, repoID string) error {
	// TODO: Implement scan cancellation
	// This would involve canceling the context for ongoing scan jobs
	log.Printf("Canceling scan for group %s in repo %s", groupName, repoID)
	return nil
}

// ClearUsageResults clears the results of a scan
func (s *UsageScannerService) ClearUsageResults(groupName, repoID string) error {
	return s.statusManager.ClearResults(groupName, repoID)
}

// GetUsageStatistics gets usage statistics
func (s *UsageScannerService) GetUsageStatistics(repoID string) (models.UsageStatistics, error) {
	// TODO: Implement usage statistics calculation
	// This would involve aggregating data from all scan results
	sources, err := s.sourceManager.GetAllSources()
	if err != nil {
		return models.UsageStatistics{}, err
	}

	activeSources := 0
	for _, source := range sources {
		if source.IsActive {
			activeSources++
		}
	}

	stats := models.UsageStatistics{
		TotalGroups:       0, // TODO: Get from repository
		GroupsWithUsage:   0, // TODO: Calculate from scan results
		TotalUsages:       0, // TODO: Calculate from scan results
		SourcesConfigured: len(sources),
		SourcesActive:     activeSources,
		LastScanTime:      time.Now(), // TODO: Get actual last scan time
	}

	return stats, nil
}

// processScan processes a scan request (called by worker pool)
func (s *UsageScannerService) processScan(ctx context.Context, request models.UsageScanRequest) {
	// This method would be called by the worker pool to process individual scan jobs
	// Implementation would be similar to ScanGroupUsage but for a single source
}

// ScanJob represents a scan job
type ScanJob struct {
	GroupName string
	RepoID    string
	Source    models.UsageSource
	Handler   SourceHandler
	Context   context.Context
}

// ScanResult represents a scan result
type ScanResult struct {
	GroupName   string
	RepoID      string
	SourceID    string
	Results     []models.UsageResult
	Error       error
	CompletedAt time.Time
}
