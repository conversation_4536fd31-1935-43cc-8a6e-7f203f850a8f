package models

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
)

// Group represents a group from the ADGitOps repository
type Group struct {
	Groupname       string           `json:"Groupname"`
	Type            string           `json:"Type"`
	Description     string           `json:"Description"`
	Members         GroupMembers     `json:"Members"`                   // Direct members as defined in JSON
	DirectMembers   *MembershipInfo  `json:"DirectMembers,omitempty"`   // Categorized direct members
	ResolvedMembers *ResolvedMembers `json:"ResolvedMembers,omitempty"` // All users with access through this group
	ParentGroups    []string         `json:"ParentGroups,omitempty"`    // Groups that contain this group
	Lob             string           `json:"LOB"`                       // LOB is derived from parent folder structure
	SourceFile      string           `json:"SourceFile,omitempty"`      // Source file path where the group was defined
	RepoID          string           `json:"repoId,omitempty"`          // ID of the repository this group belongs to
}

// MembershipInfo provides categorized information about direct members
type MembershipInfo struct {
	Users  []string `json:"users"`  // Direct user members
	Groups []string `json:"groups"` // Direct group members
	Total  int      `json:"total"`  // Total count of direct members
}

// ResolvedMembers provides flattened membership information
type ResolvedMembers struct {
	Users []ResolvedUser `json:"users"` // All users that have access through this group
	Total int            `json:"total"` // Total count of resolved users
}

// ResolvedUser represents a user with information about how they got access
type ResolvedUser struct {
	Name   string   `json:"name"`   // Username
	Path   []string `json:"path"`   // Path of groups that led to this user (e.g., ["parent-group", "child-group"])
	Direct bool     `json:"direct"` // True if user is a direct member, false if through nested groups
}

// User represents a user with their group memberships
type User struct {
	Name         string        `json:"name"`
	Groups       []string      `json:"groups"`
	LOBs         []string      `json:"lobs,omitempty"`         // List of LOBs the user belongs to
	GroupDetails []GroupDetail `json:"groupDetails,omitempty"` // Detailed information about groups
	RepoID       string        `json:"repoId,omitempty"`       // ID of the repository this user belongs to
}

// GroupDetail provides detailed information about a group membership
type GroupDetail struct {
	Name       string `json:"name"`       // Group name
	LOB        string `json:"lob"`        // Group's LOB
	SourceFile string `json:"sourceFile"` // File where the group is defined
}

// TreeNode represents a node in the groups tree structure
type TreeNode struct {
	Name        string   `json:"name"`        // Group name
	Type        string   `json:"type"`        // Group type (access_group, org_group, etc.)
	Description string   `json:"description"` // Group description
	Parents     []string `json:"parents"`     // Direct parent groups
	Children    []string `json:"children"`    // Direct child groups
	Level       int      `json:"level"`       // Depth level in the tree (0 = root)
	UserCount   int      `json:"userCount"`   // Number of direct users
	GroupCount  int      `json:"groupCount"`  // Number of direct child groups
	LOB         string   `json:"lob"`         // Line of business
}

// TreeData represents the complete tree structure
type TreeData struct {
	Roots []string             `json:"roots"` // Root group names (groups with no parents)
	Nodes map[string]*TreeNode `json:"nodes"` // Map of group name to tree node
	Stats TreeStats            `json:"stats"` // Tree statistics
}

// FilteredTreeData represents tree data with filtering applied
type FilteredTreeData struct {
	Tree          TreeData `json:"tree"`          // Complete tree structure
	MatchedGroups []string `json:"matchedGroups"` // Groups that match the filter
	RelatedGroups []string `json:"relatedGroups"` // Groups related to matched groups
	FilterApplied bool     `json:"filterApplied"` // Whether a filter was applied
	FilterQuery   string   `json:"filterQuery"`   // The filter query used
}

// TreeStats provides statistics about the tree structure
type TreeStats struct {
	TotalGroups  int `json:"totalGroups"`  // Total number of groups
	MaxDepth     int `json:"maxDepth"`     // Maximum depth of the tree
	RootGroups   int `json:"rootGroups"`   // Number of root groups
	OrphanGroups int `json:"orphanGroups"` // Number of groups with no relationships
}

// MemberType defines the type of a group member
type MemberType string

const (
	// User member type
	UserMemberType MemberType = "user"
	// Group member type (nested group)
	GroupMemberType MemberType = "group"
)

// Member represents a single member of a group with type information
type Member struct {
	Name string     `json:"name"` // Name of the member
	Type MemberType `json:"type"` // Type of member (user or group)
}

// GroupMembers is a collection of members with type information
type GroupMembers []Member

// UnmarshalJSON handles the case where members can be a single string, array of strings, or custom format
func (gm *GroupMembers) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		// Handle the case where "Members" is missing
		return nil
	}

	// First try the original string format
	var rawMembers []string
	if err := json.Unmarshal(data, &rawMembers); err == nil {
		// Convert string members to structured format
		members := make(GroupMembers, 0, len(rawMembers))
		for _, rawMember := range rawMembers {
			// Determine if this is a group reference
			memberType := UserMemberType
			memberName := rawMember

			if strings.HasPrefix(rawMember, "group:") {
				memberType = GroupMemberType
				memberName = strings.TrimPrefix(rawMember, "group:")
			}

			members = append(members, Member{
				Name: memberName,
				Type: memberType,
			})
		}
		*gm = members
		return nil
	}

	// Try as a single string
	var singleMember string
	if err := json.Unmarshal(data, &singleMember); err == nil {
		// Determine if this is a group reference
		memberType := UserMemberType
		memberName := singleMember

		if strings.HasPrefix(singleMember, "group:") {
			memberType = GroupMemberType
			memberName = strings.TrimPrefix(singleMember, "group:")
		}

		*gm = GroupMembers{Member{
			Name: memberName,
			Type: memberType,
		}}
		return nil
	}

	// Try as an array of already structured members
	var structuredMembers []Member
	if err := json.Unmarshal(data, &structuredMembers); err == nil {
		*gm = structuredMembers
		return nil
	}

	return fmt.Errorf("failed to unmarshal Members field")
}

// ToStrings converts the structured members back to the legacy string format
// This helps maintain backward compatibility with existing code
func (gm GroupMembers) ToStrings() []string {
	members := make([]string, len(gm))
	for i, member := range gm {
		if member.Type == GroupMemberType {
			members[i] = "group:" + member.Name
		} else {
			members[i] = member.Name
		}
	}
	return members
}

// RepositoryType defines the type of git repository service
type RepositoryType string

const (
	// GitLab repository type
	GitLab RepositoryType = "gitlab"
	// Bitbucket repository type
	Bitbucket RepositoryType = "bitbucket"
)

// RepositoryConfig contains the generic configuration for connecting to a git repository service
type RepositoryConfig struct {
	ID            string         `json:"id"`            // Unique identifier for this configuration
	Name          string         `json:"name"`          // Display name for this repository configuration
	Type          RepositoryType `json:"type"`          // Type of repository (gitlab or bitbucket)
	URL           string         `json:"url"`           // Base URL of the repository service
	Token         string         `json:"token"`         // Authentication token
	ProjectID     string         `json:"projectId"`     // Project ID or slug
	PollFrequency int            `json:"pollFrequency"` // In seconds
	IsActive      bool           `json:"isActive"`      // Whether this repository is currently active
	LocalRepoDir  string         `json:"localRepoDir"`  // Local directory for this repo clone

	// GitLab specific fields
	GitLabNamespace string `json:"gitlabNamespace,omitempty"` // Namespace (group/user) for GitLab

	// Bitbucket specific fields
	BitbucketWorkspace string `json:"bitbucketWorkspace,omitempty"` // Workspace/Project key for Bitbucket
	BitbucketRepoSlug  string `json:"bitbucketRepoSlug,omitempty"`  // Repository slug for Bitbucket
	BitbucketUsername  string `json:"bitbucketUsername,omitempty"`  // Username for basic auth (alternative to token)
	BitbucketPassword  string `json:"bitbucketPassword,omitempty"`  // Password for basic auth (alternative to token)
}

// RepositoryConfigList represents a list of repository configurations
type RepositoryConfigList struct {
	Configs []RepositoryConfig `json:"configs"`
}

// QueryParams contains parameters for filtering data queries
type QueryParams struct {
	LOB               string   `json:"lob"`
	Types             []string `json:"types,omitempty"`
	GroupIDs          []string `json:"groupIds,omitempty"`
	UserIDs           []string `json:"userIds,omitempty"`
	Name              string   `json:"name,omitempty"`    // Report name
	FlattenMembership bool     `json:"flattenMembership"` // Whether to recursively flatten group memberships to extract all users
}

// GetCacheKey returns a string representation of QueryParams for use as a cache key
func (q QueryParams) GetCacheKey() string {
	// Create a predictable string representation of the query
	parts := []string{}

	if q.LOB != "" {
		parts = append(parts, "lob_"+q.LOB)
	}

	if len(q.Types) > 0 {
		// Sort to ensure consistent order
		typesCopy := make([]string, len(q.Types))
		copy(typesCopy, q.Types)
		sort.Strings(typesCopy)
		parts = append(parts, "types_"+strings.Join(typesCopy, ","))
	}

	if len(q.GroupIDs) > 0 {
		// Sort to ensure consistent order
		groupsCopy := make([]string, len(q.GroupIDs))
		copy(groupsCopy, q.GroupIDs)
		sort.Strings(groupsCopy)
		parts = append(parts, "groups_"+strings.Join(groupsCopy, ","))
	}

	if len(q.UserIDs) > 0 {
		// Sort to ensure consistent order
		usersCopy := make([]string, len(q.UserIDs))
		copy(usersCopy, q.UserIDs)
		sort.Strings(usersCopy)
		parts = append(parts, "users_"+strings.Join(usersCopy, ","))
	}

	if q.Name != "" {
		parts = append(parts, "name_"+q.Name)
	}

	// Add flatten membership to cache key if true
	if q.FlattenMembership {
		parts = append(parts, "flatten_true")
	}

	if len(parts) == 0 {
		return "all"
	}

	return strings.Join(parts, "_")
}

// RepositoryStatus represents the current status of the repository
type RepositoryStatus struct {
	LastSync       string `json:"lastSync"`
	LastCommit     string `json:"lastCommit"`
	HasChanges     bool   `json:"hasChanges"`
	SyncInProgress bool   `json:"syncInProgress"`
}

// ScheduleConfig represents the configuration for automatic report generation
type ScheduleConfig struct {
	Enabled         bool   `json:"enabled"`         // Whether automatic scheduling is enabled
	Frequency       string `json:"frequency"`       // Frequency of report generation (interval, daily, weekly, monthly)
	DayOfWeek       int    `json:"dayOfWeek"`       // Day of week for weekly reports (0-6, Sunday-Saturday)
	DayOfMonth      int    `json:"dayOfMonth"`      // Day of month for monthly reports (1-31)
	Hour            int    `json:"hour"`            // Hour of day to generate report (0-23)
	Minute          int    `json:"minute"`          // Minute of hour to generate report (0-59)
	IntervalHours   int    `json:"intervalHours"`   // Hours component of interval (for interval frequency)
	IntervalMinutes int    `json:"intervalMinutes"` // Minutes component of interval (for interval frequency)
	NextRun         string `json:"nextRun"`         // Next scheduled run time
}

// ReportPreset represents a saved report configuration
type ReportPreset struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	ReportType   string         `json:"reportType"` // "users", "groups", or "both"
	Query        QueryParams    `json:"query"`
	SearchQuery  string         `json:"searchQuery"` // Raw search query for advanced filtering
	CreatedAt    string         `json:"createdAt"`
	UpdatedAt    string         `json:"updatedAt"`
	IsActive     bool           `json:"isActive"`     // Whether this preset is active or deactivated
	Version      int            `json:"version"`      // Version number of this preset
	ParentID     string         `json:"parentId"`     // ID of the original preset (for versioned presets)
	SharedID     string         `json:"sharedId"`     // Shared ID for all versions of the same preset
	Schedule     ScheduleConfig `json:"schedule"`     // Configuration for automatic report generation
	RepositoryID string         `json:"repositoryId"` // ID of the repository this preset is bound to
}

// Report represents a generated report file
type Report struct {
	ID             string                 `json:"id"`
	PresetID       string                 `json:"presetId,omitempty"`
	PresetName     string                 `json:"presetName,omitempty"`
	PresetVersion  int                    `json:"presetVersion,omitempty"`  // Version of the preset used to generate this report
	SharedPresetID string                 `json:"sharedPresetId,omitempty"` // Shared ID for all versions of the same preset
	Filename       string                 `json:"filename"`
	Type           string                 `json:"type"` // "users", "groups", or "both"
	Size           int64                  `json:"size"`
	CreatedAt      string                 `json:"createdAt"`
	DownloadURL    string                 `json:"downloadUrl"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`  // Additional metadata about the report
	Scheduled      bool                   `json:"scheduled,omitempty"` // Whether this report was generated by a schedule
	RepositoryID   string                 `json:"repositoryId"`        // ID of the repository this report is bound to
}

// ReportExecution represents a scheduled report execution record
type ReportExecution struct {
	ID             string `json:"id"`
	PresetID       string `json:"presetId"`
	PresetName     string `json:"presetName"`
	PresetVersion  int    `json:"presetVersion,omitempty"`  // Version of the preset that was executed
	SharedPresetID string `json:"sharedPresetId,omitempty"` // Shared ID for all versions of the same preset
	Status         string `json:"status"`                   // "success", "failed", "skipped"
	ExecutedAt     string `json:"executedAt"`
	ReportID       string `json:"reportId,omitempty"`     // ID of the generated report (if successful)
	ErrorMessage   string `json:"errorMessage,omitempty"` // Error message (if failed)
}

// BatchDeleteResult represents the result of a batch delete operation
type BatchDeleteResult struct {
	TotalCount      int               `json:"totalCount"`      // Total number of reports requested to delete
	SuccessCount    int               `json:"successCount"`    // Number of reports successfully deleted
	FailedCount     int               `json:"failedCount"`     // Number of reports that failed to delete
	FailedReportIDs map[string]string `json:"failedReportIds"` // Map of report IDs to error messages for failed deletions
}

// For backward compatibility
type GitLabConfig RepositoryConfig
type GitLabConfigList RepositoryConfigList

// GroupMember is a custom type to handle both string and array JSON representations
type GroupMember []string

// UnmarshalJSON handles the case where a member can be a single string or an array
func (gm *GroupMember) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		// Handle the case where "Members" is missing
		return nil
	}

	// Try unmarshal as string directly
	var singleMember string
	if err := json.Unmarshal(data, &singleMember); err == nil {
		*gm = GroupMember{singleMember}
		return nil
	}

	var rawMembers []string
	if err := json.Unmarshal(data, &rawMembers); err == nil {
		*gm = GroupMember(rawMembers)
		return nil
	}

	return fmt.Errorf("failed to unmarshal Members field")
}
