package services

import (
	"context"
	"fmt"
	"io/fs"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// GitSourceHandler handles Git repository sources
type GitSourceHandler struct {
	BaseSourceHandler
	clonePath            string
	logger               *ScanLogger // Scan logger for UI integration
	scanID               string      // Current scan ID for logging
	lastCommit           string      // Last known commit hash
	lastSyncTime         time.Time   // Last time we checked for updates
	syncInProgress       bool        // Flag to prevent concurrent syncs
	successfulAuthMethod string      // Store the successful authentication method
}

// NewGitSourceHandler creates a new Git source handler
func NewGitSourceHandler() *GitSourceHandler {
	return &GitSourceHandler{}
}

// SetScanLogger sets the scan logger and scan ID for UI integration
func (g *GitSourceHandler) SetScanLogger(logger *ScanLogger, scanID string) {
	g.logger = logger
	g.scanID = scanID
}

// loadPersistedState loads the last commit hash and sync time from files
func (g *GitSourceHandler) loadPersistedState() {
	// Load last commit hash
	commitFile := filepath.Join(g.clonePath, "COMMIT_HASH")
	if data, err := os.ReadFile(commitFile); err == nil {
		g.lastCommit = strings.TrimSpace(string(data))
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "load_state", fmt.Sprintf("Loaded last commit hash: %s", g.lastCommit), nil)
		}
	}

	// Load last sync time
	syncFile := filepath.Join(g.clonePath, "LAST_SYNC")
	if data, err := os.ReadFile(syncFile); err == nil {
		if syncTime, err := time.Parse(time.RFC3339, strings.TrimSpace(string(data))); err == nil {
			g.lastSyncTime = syncTime
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "load_state", fmt.Sprintf("Loaded last sync time: %s", g.lastSyncTime.Format(time.RFC3339)), nil)
			}
		}
	}
}

// savePersistedState saves the current commit hash and sync time to files
func (g *GitSourceHandler) savePersistedState() error {
	// Ensure directory exists
	if err := os.MkdirAll(g.clonePath, 0755); err != nil {
		return fmt.Errorf("failed to create repository directory: %w", err)
	}

	// Save commit hash
	if g.lastCommit != "" {
		commitFile := filepath.Join(g.clonePath, "COMMIT_HASH")
		if err := os.WriteFile(commitFile, []byte(g.lastCommit), 0644); err != nil {
			return fmt.Errorf("failed to save commit hash: %w", err)
		}
	}

	// Save sync time
	g.lastSyncTime = time.Now()
	syncFile := filepath.Join(g.clonePath, "LAST_SYNC")
	if err := os.WriteFile(syncFile, []byte(g.lastSyncTime.Format(time.RFC3339)), 0644); err != nil {
		return fmt.Errorf("failed to save sync time: %w", err)
	}

	return nil
}

// needsSync determines if the repository needs to be synced based on time and commit hash
func (g *GitSourceHandler) needsSync(ctx context.Context) (bool, error) {
	// Get sync interval from source configuration (default to 5 minutes)
	syncInterval := 5 * time.Minute
	if g.source.GitConfig != nil && g.source.GitConfig.SyncInterval > 0 {
		syncInterval = time.Duration(g.source.GitConfig.SyncInterval) * time.Second
	}

	// Check if enough time has passed since last sync
	if time.Since(g.lastSyncTime) < syncInterval {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_check", fmt.Sprintf("Skipping sync - last sync was %v ago (interval: %v)", time.Since(g.lastSyncTime), syncInterval), nil)
		}
		return false, nil
	}

	// Check if repository exists locally
	if _, err := os.Stat(filepath.Join(g.clonePath, ".git")); os.IsNotExist(err) {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_check", "Repository doesn't exist locally - needs initial clone", nil)
		}
		return true, nil
	}

	// Check remote commit hash
	remoteCommit, err := g.getRemoteCommitHash(ctx)
	if err != nil {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogWarning(g.scanID, "sync_check", "Failed to get remote commit hash, will sync anyway", err.Error())
		}
		return true, nil // Sync on error to be safe
	}

	// Compare with local commit hash
	needsSync := g.lastCommit != remoteCommit
	if g.logger != nil && g.scanID != "" {
		if needsSync {
			g.logger.LogStep(g.scanID, "sync_check", fmt.Sprintf("Repository needs sync - local: %s, remote: %s", g.lastCommit, remoteCommit), nil)
		} else {
			g.logger.LogStep(g.scanID, "sync_check", fmt.Sprintf("Repository is up to date - commit: %s", g.lastCommit), nil)
		}
	}

	return needsSync, nil
}

// getRemoteCommitHash gets the latest commit hash from the remote repository
func (g *GitSourceHandler) getRemoteCommitHash(ctx context.Context) (string, error) {
	// Use git ls-remote to get the latest commit hash without cloning
	args := []string{"ls-remote", g.source.GitConfig.RepoURL, g.source.GitConfig.Branch}
	cmd := g.createAuthenticatedGitCommand(ctx, args...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("git ls-remote failed: %s", string(output))
	}

	// Parse the output to get the commit hash
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	if len(lines) == 0 {
		return "", fmt.Errorf("no output from git ls-remote")
	}

	// The first line should contain the commit hash
	parts := strings.Fields(lines[0])
	if len(parts) == 0 {
		return "", fmt.Errorf("invalid output from git ls-remote")
	}

	commitHash := parts[0]
	if len(commitHash) < 7 {
		return "", fmt.Errorf("invalid commit hash: %s", commitHash)
	}

	return commitHash, nil
}

// Initialize initializes the Git source handler
func (g *GitSourceHandler) Initialize(source models.UsageSource) error {
	if err := g.BaseSourceHandler.Initialize(source); err != nil {
		return err
	}

	if source.GitConfig == nil {
		return models.NewValidationError("gitConfig", "git configuration is required")
	}

	// Set up persistent clone path (not in temp directory)
	if source.GitConfig.ClonePath != "" {
		g.clonePath = source.GitConfig.ClonePath
	} else {
		// Use persistent storage directory similar to repository system
		dataDir := "data" // TODO: Make this configurable
		g.clonePath = filepath.Join(dataDir, "usage-sources", "git-repos", source.ID)
	}

	// Load existing commit hash and sync time if available
	g.loadPersistedState()

	return nil
}

// GetSourceType returns the source type
func (g *GitSourceHandler) GetSourceType() models.SourceType {
	return models.SourceTypeGit
}

// ValidateConfig validates the Git source configuration
func (g *GitSourceHandler) ValidateConfig(source models.UsageSource) error {
	if err := g.BaseSourceHandler.ValidateConfig(source); err != nil {
		return err
	}

	if source.GitConfig == nil {
		return models.NewValidationError("gitConfig", "git configuration is required")
	}

	return source.GitConfig.Validate()
}

// TestConnection tests the connection to the Git repository
func (g *GitSourceHandler) TestConnection(ctx context.Context) error {
	if g.source.GitConfig == nil {
		return fmt.Errorf("git configuration not initialized")
	}

	// For Bitbucket, try multiple authentication formats
	if strings.Contains(g.source.GitConfig.RepoURL, "bitbucket") && g.source.GitConfig.AuthType == "token" {
		return g.testBitbucketConnection(ctx)
	}

	// Standard connection test for other Git providers
	return g.testStandardConnection(ctx)
}

// testBitbucketConnection tries multiple authentication formats for Bitbucket
func (g *GitSourceHandler) testBitbucketConnection(ctx context.Context) error {
	authFormats := []struct {
		name        string
		useHeader   bool
		username    string
		password    string
		description string
	}{
		{"bearer-header", true, "", "", "HTTP Bearer token header (Bitbucket 8.1+)"},
		{"token-as-username", false, g.source.GitConfig.Token, "", "Token as username (legacy)"},
		{"x-token-auth", false, "x-token-auth", g.source.GitConfig.Token, "x-token-auth format (legacy)"},
		{"token-as-password", false, "", g.source.GitConfig.Token, "Token as password (legacy)"},
	}

	for i, format := range authFormats {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "bitbucket_auth_test", fmt.Sprintf("Trying Bitbucket auth format %d: %s - %s", i+1, format.name, format.description), nil)
		}

		var cmd *exec.Cmd
		var cmdStr string

		if format.useHeader {
			// Use HTTP Bearer token header for Bitbucket Server 8.1+
			authHeader := fmt.Sprintf("Authorization: Bearer %s", g.source.GitConfig.Token)
			cmd = exec.CommandContext(ctx, "git",
				"-c", "credential.helper=", // Disable credential helper
				"-c", "core.askPass=", // Disable askpass
				"-c", fmt.Sprintf("http.extraHeader=%s", authHeader), // Add Bearer token header
				"ls-remote", g.source.GitConfig.RepoURL)

			cmdStr = fmt.Sprintf("git -c credential.helper= -c core.askPass= -c \"http.extraHeader=Authorization: Bearer ***\" ls-remote %s", g.source.GitConfig.RepoURL)
		} else {
			// Use URL-based authentication (legacy methods)
			authenticatedURL := g.buildAuthenticatedURL(g.source.GitConfig.RepoURL, format.username, format.password)
			cmd = exec.CommandContext(ctx, "git",
				"-c", "credential.helper=", // Disable credential helper
				"-c", "core.askPass=", // Disable askpass
				"ls-remote", authenticatedURL)

			cmdStr = g.sanitizeCommandForLogging(cmd)
		}

		// Set up environment
		g.setupGitAuthEnvironment(cmd)

		// Log the command being executed (without sensitive credentials)
		log.Printf("Executing Git command for source %s (format: %s): %s", g.source.Name, format.name, cmdStr)

		output, err := cmd.CombinedOutput()
		if err == nil {
			log.Printf("Git connection test successful for source %s using format: %s", g.source.Name, format.name)
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "bitbucket_auth_success", fmt.Sprintf("Bitbucket authentication successful with format: %s", format.name), nil)
			}

			// Store the successful authentication method for later use
			g.successfulAuthMethod = format.name
			g.updateStatus(true, nil)
			return nil
		}

		// Log the failure and try next format
		log.Printf("Git ls-remote failed for source %s with format %s: %v\nOutput: %s", g.source.Name, format.name, err, string(output))
		if g.logger != nil && g.scanID != "" {
			g.logger.LogWarning(g.scanID, "bitbucket_auth_attempt", fmt.Sprintf("Auth format '%s' failed: %s", format.name, string(output)), "")
		}
	}

	// All formats failed
	errorMsg := "All Bitbucket authentication formats failed"
	if g.logger != nil && g.scanID != "" {
		g.logger.LogError(g.scanID, "bitbucket_auth_failed", errorMsg, "Tried Bearer header (8.1+), token-as-username, x-token-auth, and token-as-password formats", g.source.ID)
	}
	g.updateStatus(false, fmt.Errorf(errorMsg))
	return fmt.Errorf(errorMsg)
}

// testStandardConnection performs standard Git connection test
func (g *GitSourceHandler) testStandardConnection(ctx context.Context) error {
	// Test by doing a shallow clone or ls-remote with credential helper disabled
	cmd := exec.CommandContext(ctx, "git",
		"-c", "credential.helper=", // Disable credential helper
		"-c", "core.askPass=", // Disable askpass
		"ls-remote", g.source.GitConfig.RepoURL)

	// Set up authentication if needed
	if err := g.setupGitAuth(cmd); err != nil {
		errorMsg := fmt.Sprintf("Git auth setup failed for source %s: %v", g.source.Name, err)
		log.Printf(errorMsg)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogError(g.scanID, "git_auth_setup", "Failed to setup Git authentication", err.Error(), g.source.ID)
		}
		g.updateStatus(false, err)
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	// Log the command being executed (without sensitive credentials)
	cmdStr := g.sanitizeCommandForLogging(cmd)
	log.Printf("Executing Git command for source %s: %s", g.source.Name, cmdStr)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_connection_test", fmt.Sprintf("Testing Git connection: %s", cmdStr), nil)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		errorMsg := fmt.Sprintf("Git ls-remote failed for source %s: %v\nOutput: %s", g.source.Name, err, string(output))
		log.Printf(errorMsg)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogError(g.scanID, "git_connection_test", "Git connection test failed", string(output), g.source.ID)
		}
		g.updateStatus(false, fmt.Errorf("git ls-remote failed: %s", string(output)))
		return fmt.Errorf("failed to connect to git repository: %w", err)
	}

	log.Printf("Git connection test successful for source %s", g.source.Name)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_connection_test", "Git connection test successful", nil)
	}
	g.updateStatus(true, nil)
	return nil
}

// ScanForGroupUsage scans the Git repository for group usage
func (g *GitSourceHandler) ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error) {
	if g.source.GitConfig == nil {
		return nil, fmt.Errorf("git configuration not initialized")
	}

	// Clone or update the repository
	if err := g.cloneOrUpdateRepository(ctx); err != nil {
		return nil, fmt.Errorf("failed to clone/update repository: %w", err)
	}

	// Scan files for group usage
	results, err := g.scanRepositoryFiles(groupName)
	if err != nil {
		return nil, fmt.Errorf("failed to scan repository files: %w", err)
	}

	// Update results with source information
	for i := range results {
		results[i].ID = uuid.New().String()
		results[i].GroupName = groupName
		results[i].SourceID = g.source.ID
		results[i].SourceName = g.source.Name
		results[i].SourceType = models.SourceTypeGit
		results[i].DetectedAt = time.Now()

		// Add Git-specific metadata
		if commitHash, err := g.getLatestCommitHash(); err == nil {
			results[i].CommitHash = commitHash
		}
		results[i].Branch = g.source.GitConfig.Branch
	}

	return results, nil
}

// Cleanup cleans up resources
func (g *GitSourceHandler) Cleanup() error {
	// Optionally remove the clone directory
	// For now, we'll keep it for performance (incremental updates)
	return nil
}

// createAuthenticatedGitCommand creates a Git command with the appropriate authentication method
func (g *GitSourceHandler) createAuthenticatedGitCommand(ctx context.Context, args ...string) *exec.Cmd {
	var cmd *exec.Cmd

	// Use the successful authentication method if available, otherwise default to Bearer header
	authMethod := g.successfulAuthMethod
	if authMethod == "" {
		authMethod = "bearer-header" // Default to modern Bitbucket authentication
	}

	if strings.Contains(g.source.GitConfig.RepoURL, "bitbucket") && authMethod == "bearer-header" {
		// Use HTTP Bearer token header for Bitbucket Server 8.1+
		authHeader := fmt.Sprintf("Authorization: Bearer %s", g.source.GitConfig.Token)
		gitArgs := []string{
			"-c", "credential.helper=", // Disable credential helper
			"-c", "core.askPass=", // Disable askpass
			"-c", fmt.Sprintf("http.extraHeader=%s", authHeader), // Add Bearer token header
		}
		gitArgs = append(gitArgs, args...)
		cmd = exec.CommandContext(ctx, "git", gitArgs...)
	} else {
		// Use standard authentication setup
		gitArgs := []string{
			"-c", "credential.helper=", // Disable credential helper
			"-c", "core.askPass=", // Disable askpass
		}
		gitArgs = append(gitArgs, args...)
		cmd = exec.CommandContext(ctx, "git", gitArgs...)

		// Set up URL-based authentication if needed
		g.setupGitAuth(cmd)
	}

	// Set up environment
	g.setupGitAuthEnvironment(cmd)

	return cmd
}

// cloneOrUpdateRepository clones or updates the Git repository using smart sync logic
func (g *GitSourceHandler) cloneOrUpdateRepository(ctx context.Context) error {
	// Prevent concurrent syncs
	if g.syncInProgress {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_check", "Sync already in progress, skipping", nil)
		}
		return nil
	}

	g.syncInProgress = true
	defer func() {
		g.syncInProgress = false
	}()

	// Check if we need to sync
	needsSync, err := g.needsSync(ctx)
	if err != nil {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogWarning(g.scanID, "sync_check", "Failed to check if sync is needed, proceeding with sync", err.Error())
		}
		needsSync = true // Sync on error to be safe
	}

	if !needsSync {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_skip", "Repository is up to date, skipping sync", nil)
		}
		return nil
	}

	// Check if repository already exists locally
	if _, err := os.Stat(filepath.Join(g.clonePath, ".git")); os.IsNotExist(err) {
		// Repository doesn't exist, clone it
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_action", "Repository doesn't exist locally, cloning", nil)
		}
		if err := g.cloneRepository(ctx); err != nil {
			return err
		}
	} else if err != nil {
		return fmt.Errorf("failed to check repository status: %w", err)
	} else {
		// Repository exists, update it
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_action", "Repository exists locally, updating", nil)
		}
		if err := g.updateRepository(ctx); err != nil {
			return err
		}
	}

	// Update the commit hash and save state
	if remoteCommit, err := g.getRemoteCommitHash(ctx); err == nil {
		g.lastCommit = remoteCommit
		if err := g.savePersistedState(); err != nil {
			if g.logger != nil && g.scanID != "" {
				g.logger.LogWarning(g.scanID, "save_state", "Failed to save repository state", err.Error())
			}
		} else {
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "save_state", fmt.Sprintf("Saved repository state - commit: %s", g.lastCommit), nil)
			}
		}
	}

	return nil
}

// cloneRepository clones the Git repository
func (g *GitSourceHandler) cloneRepository(ctx context.Context) error {
	// Create parent directory
	if err := os.MkdirAll(filepath.Dir(g.clonePath), 0755); err != nil {
		log.Printf("Failed to create clone directory for source %s: %v", g.source.Name, err)
		return fmt.Errorf("failed to create clone directory: %w", err)
	}

	// Clone the repository using the authenticated command helper
	args := []string{"clone", "--depth", "1"}
	if g.source.GitConfig.Branch != "" {
		args = append(args, "--branch", g.source.GitConfig.Branch)
	}

	// For Bitbucket with Bearer authentication, use the original URL
	// For other methods, the helper will handle URL modification
	if strings.Contains(g.source.GitConfig.RepoURL, "bitbucket") && g.successfulAuthMethod == "bearer-header" {
		args = append(args, g.source.GitConfig.RepoURL, g.clonePath)
	} else {
		args = append(args, g.source.GitConfig.RepoURL, g.clonePath)
	}

	cmd := g.createAuthenticatedGitCommand(ctx, args...)

	// Log the command being executed (without sensitive credentials)
	cmdStr := g.sanitizeCommandForLogging(cmd)
	log.Printf("Executing Git clone for source %s: %s", g.source.Name, cmdStr)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_clone", fmt.Sprintf("Cloning Git repository: %s", cmdStr), nil)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		errorMsg := fmt.Sprintf("Git clone failed for source %s: %v\nOutput: %s", g.source.Name, err, string(output))
		log.Printf(errorMsg)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogError(g.scanID, "git_clone", "Git clone operation failed", string(output), g.source.ID)
		}
		return fmt.Errorf("git clone failed: %s", string(output))
	}

	log.Printf("Successfully cloned repository %s to %s", g.source.GitConfig.RepoURL, g.clonePath)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_clone", "Git repository cloned successfully", nil)
	}
	return nil
}

// updateRepository updates the existing Git repository
func (g *GitSourceHandler) updateRepository(ctx context.Context) error {
	// Change to repository directory with credential helper disabled
	cmd := exec.CommandContext(ctx, "git",
		"-c", "credential.helper=", // Disable credential helper
		"-c", "core.askPass=", // Disable askpass
		"pull", "origin", g.source.GitConfig.Branch)
	cmd.Dir = g.clonePath

	// Set up authentication
	if err := g.setupGitAuth(cmd); err != nil {
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("git pull failed: %s", string(output))
	}

	log.Printf("Successfully updated repository %s", g.source.GitConfig.RepoURL)
	return nil
}

// setupGitAuthEnvironment sets up only the environment variables for Git authentication
func (g *GitSourceHandler) setupGitAuthEnvironment(cmd *exec.Cmd) {
	// Always disable interactive prompts, credential helpers, and set timeout
	globalConfigPath := "/dev/null"
	if strings.Contains(strings.ToLower(os.Getenv("OS")), "windows") {
		globalConfigPath = "NUL" // Windows equivalent of /dev/null
	}

	cmd.Env = append(os.Environ(),
		"GIT_TERMINAL_PROMPT=0",               // Disable interactive prompts
		"GIT_ASKPASS=echo",                    // Disable password prompts
		"GIT_CONFIG_NOSYSTEM=1",               // Ignore system Git config
		"GIT_CONFIG_GLOBAL="+globalConfigPath, // Ignore global Git config
		"GIT_CREDENTIAL_HELPER=",              // Disable credential helpers
		"GIT_SSH_COMMAND=ssh -o BatchMode=yes -o StrictHostKeyChecking=no", // Non-interactive SSH
	)
}

// setupGitAuth sets up Git authentication for the command
func (g *GitSourceHandler) setupGitAuth(cmd *exec.Cmd) error {
	config := g.source.GitConfig

	// Always disable interactive prompts, credential helpers, and set timeout
	globalConfigPath := "/dev/null"
	if strings.Contains(strings.ToLower(os.Getenv("OS")), "windows") {
		globalConfigPath = "NUL" // Windows equivalent of /dev/null
	}

	cmd.Env = append(os.Environ(),
		"GIT_TERMINAL_PROMPT=0",               // Disable interactive prompts
		"GIT_ASKPASS=echo",                    // Disable password prompts
		"GIT_CONFIG_NOSYSTEM=1",               // Ignore system Git config
		"GIT_CONFIG_GLOBAL="+globalConfigPath, // Ignore global Git config
		"GIT_CREDENTIAL_HELPER=",              // Disable credential helpers
		"GIT_SSH_COMMAND=ssh -o BatchMode=yes -o StrictHostKeyChecking=no", // Non-interactive SSH
	)

	switch config.AuthType {
	case "token":
		if config.Token != "" {
			var authenticatedURL string

			// Check if this is a Bitbucket URL (requires special handling)
			if strings.Contains(config.RepoURL, "bitbucket") {
				// Try multiple Bitbucket authentication formats
				// Format 1: token as username (most common for Bitbucket Server)
				authenticatedURL = g.buildAuthenticatedURL(config.RepoURL, config.Token, "")
				log.Printf("Using Bitbucket Server token as username authentication for Git repository")
				if g.logger != nil && g.scanID != "" {
					g.logger.LogStep(g.scanID, "auth_setup", "Using Bitbucket token as username authentication", nil)
				}
			} else {
				// For GitHub/GitLab, use token as username with empty password
				authenticatedURL = g.buildAuthenticatedURL(config.RepoURL, config.Token, "")
				log.Printf("Using token authentication for Git repository")
			}

			// Replace the original URL in the command arguments
			for i, arg := range cmd.Args {
				if arg == config.RepoURL {
					cmd.Args[i] = authenticatedURL
					break
				}
			}
		}
	case "basic":
		if config.Username != "" && config.Password != "" {
			// For basic auth, modify the URL to include credentials
			authenticatedURL := g.buildAuthenticatedURL(config.RepoURL, config.Username, config.Password)

			// Replace the original URL in the command arguments
			for i, arg := range cmd.Args {
				if arg == config.RepoURL {
					cmd.Args[i] = authenticatedURL
					break
				}
			}

			log.Printf("Using basic authentication for Git repository (username: %s)", config.Username)
		}
	case "none":
		// No authentication needed
		log.Printf("Using no authentication for Git repository")
	default:
		return fmt.Errorf("unsupported auth type: %s", config.AuthType)
	}

	return nil
}

// buildAuthenticatedURL builds a Git URL with embedded credentials
func (g *GitSourceHandler) buildAuthenticatedURL(repoURL, username, password string) string {
	// Parse the URL to inject credentials
	if strings.HasPrefix(repoURL, "https://") {
		// Remove https:// prefix
		urlWithoutScheme := strings.TrimPrefix(repoURL, "https://")

		// Build authenticated URL
		if password != "" {
			return fmt.Sprintf("https://%s:%s@%s", username, password, urlWithoutScheme)
		} else {
			// Token-based auth (token as username, empty password)
			return fmt.Sprintf("https://%s@%s", username, urlWithoutScheme)
		}
	}

	// For SSH URLs or other schemes, return as-is
	return repoURL
}

// sanitizeCommandForLogging creates a safe version of the command for logging (removes credentials)
func (g *GitSourceHandler) sanitizeCommandForLogging(cmd *exec.Cmd) string {
	args := make([]string, len(cmd.Args))
	copy(args, cmd.Args)

	// Replace any URLs that contain credentials with sanitized versions
	for i, arg := range args {
		if strings.Contains(arg, "@") && (strings.HasPrefix(arg, "https://") || strings.HasPrefix(arg, "http://")) {
			// This looks like a URL with credentials, sanitize it
			if strings.HasPrefix(arg, "https://") {
				// Extract the part after the credentials
				parts := strings.SplitN(arg, "@", 2)
				if len(parts) == 2 {
					args[i] = "https://***:***@" + parts[1]
				}
			} else if strings.HasPrefix(arg, "http://") {
				parts := strings.SplitN(arg, "@", 2)
				if len(parts) == 2 {
					args[i] = "http://***:***@" + parts[1]
				}
			}
		}
	}

	return strings.Join(args, " ")
}

// scanRepositoryFiles scans repository files for group usage
func (g *GitSourceHandler) scanRepositoryFiles(groupName string) ([]models.UsageResult, error) {
	var allResults []models.UsageResult

	err := filepath.WalkDir(g.clonePath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// Skip .git directory
		if d.IsDir() && d.Name() == ".git" {
			return filepath.SkipDir
		}

		// Skip directories
		if d.IsDir() {
			return nil
		}

		// Get relative path from repository root
		relPath, err := filepath.Rel(g.clonePath, path)
		if err != nil {
			return err
		}

		// Check if file matches include/exclude patterns
		if !matchesPatterns(relPath, g.source.IncludePatterns, g.source.ExcludePatterns) {
			return nil
		}

		// Read file content
		content, err := os.ReadFile(path)
		if err != nil {
			log.Printf("Warning: Failed to read file %s: %v", path, err)
			return nil // Continue with other files
		}

		// Search for group usage in content
		results := findGroupUsageInContent(string(content), groupName, relPath)

		// Add file metadata
		for i := range results {
			if info, err := d.Info(); err == nil {
				results[i].FileSize = info.Size()
			}
			results[i].FileType = getFileExtension(relPath)
		}

		allResults = append(allResults, results...)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk repository directory: %w", err)
	}

	return allResults, nil
}

// getLatestCommitHash gets the latest commit hash
func (g *GitSourceHandler) getLatestCommitHash() (string, error) {
	cmd := exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = g.clonePath

	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(output)), nil
}

// getFileExtension extracts the file extension from a path
func getFileExtension(filePath string) string {
	ext := filepath.Ext(filePath)
	if len(ext) > 1 {
		return ext[1:] // Remove the leading dot
	}
	return ""
}
