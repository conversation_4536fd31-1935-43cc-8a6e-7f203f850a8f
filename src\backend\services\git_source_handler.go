package services

import (
	"context"
	"fmt"
	"io/fs"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// GitSourceHandler handles Git repository sources
type GitSourceHandler struct {
	BaseSourceHandler
	clonePath string
}

// NewGitSourceHandler creates a new Git source handler
func NewGitSourceHandler() *GitSourceHandler {
	return &GitSourceHandler{}
}

// Initialize initializes the Git source handler
func (g *GitSourceHandler) Initialize(source models.UsageSource) error {
	if err := g.BaseSourceHandler.Initialize(source); err != nil {
		return err
	}

	if source.GitConfig == nil {
		return models.NewValidationError("gitConfig", "git configuration is required")
	}

	// Set up clone path
	if source.GitConfig.ClonePath != "" {
		g.clonePath = source.GitConfig.ClonePath
	} else {
		// Generate a unique clone path
		tempDir := os.TempDir()
		g.clonePath = filepath.Join(tempDir, "adgitops-usage-tracking", "git-clones", source.ID)
	}

	return nil
}

// GetSourceType returns the source type
func (g *GitSourceHandler) GetSourceType() models.SourceType {
	return models.SourceTypeGit
}

// ValidateConfig validates the Git source configuration
func (g *GitSourceHandler) ValidateConfig(source models.UsageSource) error {
	if err := g.BaseSourceHandler.ValidateConfig(source); err != nil {
		return err
	}

	if source.GitConfig == nil {
		return models.NewValidationError("gitConfig", "git configuration is required")
	}

	return source.GitConfig.Validate()
}

// TestConnection tests the connection to the Git repository
func (g *GitSourceHandler) TestConnection(ctx context.Context) error {
	if g.source.GitConfig == nil {
		return fmt.Errorf("git configuration not initialized")
	}

	// Test by doing a shallow clone or ls-remote
	cmd := exec.CommandContext(ctx, "git", "ls-remote", g.source.GitConfig.RepoURL)

	// Set up authentication if needed
	if err := g.setupGitAuth(cmd); err != nil {
		g.updateStatus(false, err)
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		g.updateStatus(false, fmt.Errorf("git ls-remote failed: %s", string(output)))
		return fmt.Errorf("failed to connect to git repository: %w", err)
	}

	g.updateStatus(true, nil)
	return nil
}

// ScanForGroupUsage scans the Git repository for group usage
func (g *GitSourceHandler) ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error) {
	if g.source.GitConfig == nil {
		return nil, fmt.Errorf("git configuration not initialized")
	}

	// Clone or update the repository
	if err := g.cloneOrUpdateRepository(ctx); err != nil {
		return nil, fmt.Errorf("failed to clone/update repository: %w", err)
	}

	// Scan files for group usage
	results, err := g.scanRepositoryFiles(groupName)
	if err != nil {
		return nil, fmt.Errorf("failed to scan repository files: %w", err)
	}

	// Update results with source information
	for i := range results {
		results[i].ID = uuid.New().String()
		results[i].GroupName = groupName
		results[i].SourceID = g.source.ID
		results[i].SourceName = g.source.Name
		results[i].SourceType = models.SourceTypeGit
		results[i].DetectedAt = time.Now()

		// Add Git-specific metadata
		if commitHash, err := g.getLatestCommitHash(); err == nil {
			results[i].CommitHash = commitHash
		}
		results[i].Branch = g.source.GitConfig.Branch
	}

	return results, nil
}

// Cleanup cleans up resources
func (g *GitSourceHandler) Cleanup() error {
	// Optionally remove the clone directory
	// For now, we'll keep it for performance (incremental updates)
	return nil
}

// cloneOrUpdateRepository clones or updates the Git repository
func (g *GitSourceHandler) cloneOrUpdateRepository(ctx context.Context) error {
	// Check if repository already exists
	if _, err := os.Stat(filepath.Join(g.clonePath, ".git")); err == nil {
		// Repository exists, update it
		return g.updateRepository(ctx)
	}

	// Repository doesn't exist, clone it
	return g.cloneRepository(ctx)
}

// cloneRepository clones the Git repository
func (g *GitSourceHandler) cloneRepository(ctx context.Context) error {
	// Create parent directory
	if err := os.MkdirAll(filepath.Dir(g.clonePath), 0755); err != nil {
		return fmt.Errorf("failed to create clone directory: %w", err)
	}

	// Clone the repository
	args := []string{"clone", "--depth", "1"}
	if g.source.GitConfig.Branch != "" {
		args = append(args, "--branch", g.source.GitConfig.Branch)
	}
	args = append(args, g.source.GitConfig.RepoURL, g.clonePath)

	cmd := exec.CommandContext(ctx, "git", args...)

	// Set up authentication
	if err := g.setupGitAuth(cmd); err != nil {
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("git clone failed: %s", string(output))
	}

	log.Printf("Successfully cloned repository %s to %s", g.source.GitConfig.RepoURL, g.clonePath)
	return nil
}

// updateRepository updates the existing Git repository
func (g *GitSourceHandler) updateRepository(ctx context.Context) error {
	// Change to repository directory
	cmd := exec.CommandContext(ctx, "git", "pull", "origin", g.source.GitConfig.Branch)
	cmd.Dir = g.clonePath

	// Set up authentication
	if err := g.setupGitAuth(cmd); err != nil {
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("git pull failed: %s", string(output))
	}

	log.Printf("Successfully updated repository %s", g.source.GitConfig.RepoURL)
	return nil
}

// setupGitAuth sets up Git authentication for the command
func (g *GitSourceHandler) setupGitAuth(cmd *exec.Cmd) error {
	config := g.source.GitConfig

	// Always disable interactive prompts and set timeout
	cmd.Env = append(os.Environ(),
		"GIT_TERMINAL_PROMPT=0", // Disable interactive prompts
		"GIT_ASKPASS=echo",      // Disable password prompts
		"GIT_SSH_COMMAND=ssh -o BatchMode=yes -o StrictHostKeyChecking=no", // Non-interactive SSH
	)

	switch config.AuthType {
	case "token":
		if config.Token != "" {
			// For token-based auth, modify the URL to include credentials
			// This is the most reliable method for automated systems
			authenticatedURL := g.buildAuthenticatedURL(config.RepoURL, config.Token, "")

			// Replace the original URL in the command arguments
			for i, arg := range cmd.Args {
				if arg == config.RepoURL {
					cmd.Args[i] = authenticatedURL
					break
				}
			}

			log.Printf("Using token authentication for Git repository")
		}
	case "basic":
		if config.Username != "" && config.Password != "" {
			// For basic auth, modify the URL to include credentials
			authenticatedURL := g.buildAuthenticatedURL(config.RepoURL, config.Username, config.Password)

			// Replace the original URL in the command arguments
			for i, arg := range cmd.Args {
				if arg == config.RepoURL {
					cmd.Args[i] = authenticatedURL
					break
				}
			}

			log.Printf("Using basic authentication for Git repository (username: %s)", config.Username)
		}
	case "none":
		// No authentication needed
		log.Printf("Using no authentication for Git repository")
	default:
		return fmt.Errorf("unsupported auth type: %s", config.AuthType)
	}

	return nil
}

// buildAuthenticatedURL builds a Git URL with embedded credentials
func (g *GitSourceHandler) buildAuthenticatedURL(repoURL, username, password string) string {
	// Parse the URL to inject credentials
	if strings.HasPrefix(repoURL, "https://") {
		// Remove https:// prefix
		urlWithoutScheme := strings.TrimPrefix(repoURL, "https://")

		// Build authenticated URL
		if password != "" {
			return fmt.Sprintf("https://%s:%s@%s", username, password, urlWithoutScheme)
		} else {
			// Token-based auth (token as username, empty password)
			return fmt.Sprintf("https://%s@%s", username, urlWithoutScheme)
		}
	}

	// For SSH URLs or other schemes, return as-is
	return repoURL
}

// scanRepositoryFiles scans repository files for group usage
func (g *GitSourceHandler) scanRepositoryFiles(groupName string) ([]models.UsageResult, error) {
	var allResults []models.UsageResult

	err := filepath.WalkDir(g.clonePath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// Skip .git directory
		if d.IsDir() && d.Name() == ".git" {
			return filepath.SkipDir
		}

		// Skip directories
		if d.IsDir() {
			return nil
		}

		// Get relative path from repository root
		relPath, err := filepath.Rel(g.clonePath, path)
		if err != nil {
			return err
		}

		// Check if file matches include/exclude patterns
		if !matchesPatterns(relPath, g.source.IncludePatterns, g.source.ExcludePatterns) {
			return nil
		}

		// Read file content
		content, err := os.ReadFile(path)
		if err != nil {
			log.Printf("Warning: Failed to read file %s: %v", path, err)
			return nil // Continue with other files
		}

		// Search for group usage in content
		results := findGroupUsageInContent(string(content), groupName, relPath)

		// Add file metadata
		for i := range results {
			if info, err := d.Info(); err == nil {
				results[i].FileSize = info.Size()
			}
			results[i].FileType = getFileExtension(relPath)
		}

		allResults = append(allResults, results...)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk repository directory: %w", err)
	}

	return allResults, nil
}

// getLatestCommitHash gets the latest commit hash
func (g *GitSourceHandler) getLatestCommitHash() (string, error) {
	cmd := exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = g.clonePath

	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(output)), nil
}

// getFileExtension extracts the file extension from a path
func getFileExtension(filePath string) string {
	ext := filepath.Ext(filePath)
	if len(ext) > 1 {
		return ext[1:] // Remove the leading dot
	}
	return ""
}
