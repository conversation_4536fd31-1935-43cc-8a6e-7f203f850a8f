[{"id": "preset_1752824761944048800", "name": "Test Preset", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-18T09:46:01+02:00", "updatedAt": "2025-07-18T09:46:01+02:00", "isActive": true, "version": 1, "parentId": "preset_1752824761944048800", "sharedId": "shared_1752824761944048800", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1752824761981744600", "name": "Updated Name", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-18T09:46:01+02:00", "updatedAt": "2025-07-18T09:46:02+02:00", "isActive": false, "version": 2, "parentId": "preset_1752824761944048800", "sharedId": "shared_1752824761944048800", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1752824762125484200", "name": "Marketing Groups", "description": "", "reportType": "groups", "query": {"lob": "Marketing", "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-18T09:46:02+02:00", "updatedAt": "2025-07-18T09:46:02+02:00", "isActive": true, "version": 1, "parentId": "preset_1752824762125484200", "sharedId": "shared_1752824762125484200", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1752824762181309700", "name": "Toggle Test Preset", "description": "Test preset for toggling activation", "reportType": "users", "query": {"lob": "Marketing", "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-07-18T09:46:02+02:00", "updatedAt": "2025-07-18T09:46:02+02:00", "isActive": true, "version": 1, "parentId": "preset_1752824762181309700", "sharedId": "shared_1752824762181309700", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}]