import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  RefreshCw,
  Search,
  Filter,
  Download,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader,
  X
} from 'lucide-react';

import { scanLogsApi } from '@/api/client';
import type {
  ScanLogEntry,
  ScanLogSummary,
  ScanLogsOverview,
  ScanLogFilter,
  ScanStatus,
  ScanLogLevel
} from '@/types/scanLogs';
import {
  ScanStatusColors,
  ScanStatusIcons,
  LogLevelColors,
  LogLevelIcons,
  formatDuration,
  formatTimestamp,
  formatRelativeTime,
  getScanStatusText,
  getLogLevelText
} from '@/types/scanLogs';

interface ScanLogsPageProps {}

const ScanLogsPage: React.FC<ScanLogsPageProps> = () => {
  // Component mount logging
  useEffect(() => {
    console.log('ScanLogsPage component mounted');
    return () => {
      console.log('ScanLogsPage component unmounted');
    };
  }, []);

  const [overview, setOverview] = useState<ScanLogsOverview | null>(null);
  const [logs, setLogs] = useState<ScanLogEntry[]>([]);
  const [summaries, setSummaries] = useState<ScanLogSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [filter, setFilter] = useState<ScanLogFilter>({
    page: 1,
    pageSize: 50
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<ScanLogLevel | ''>('');
  const [selectedStatus, setSelectedStatus] = useState<ScanStatus | ''>('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Pagination state
  const [totalPages, setTotalPages] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);

  // Auto-refresh state
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Load overview data
  const loadOverview = useCallback(async () => {
    try {
      console.log('Loading scan logs overview...');
      const overviewData = await scanLogsApi.getOverview();
      console.log('Scan logs overview loaded:', overviewData);
      setOverview(overviewData);
    } catch (err) {
      console.error('Failed to load scan logs overview:', err);
      setError('Failed to load overview data');
      setOverview(null);
    }
  }, []);

  // Load logs data
  const loadLogs = useCallback(async () => {
    try {
      const currentFilter = {
        ...filter,
        searchQuery: searchQuery || undefined,
        level: selectedLevel || undefined,
        status: selectedStatus || undefined,
        startTime: startDate || undefined,
        endTime: endDate || undefined
      };

      console.log('Loading scan logs with filter:', currentFilter);
      const response = await scanLogsApi.getLogs(currentFilter);
      console.log('Scan logs loaded:', response);
      setLogs(response.logs || []);
      setTotalPages(response.totalPages || 1);
      setTotalLogs(response.total || 0);
    } catch (err) {
      console.error('Failed to load scan logs:', err);
      setError('Failed to load scan logs');
      setLogs([]);
      setTotalPages(1);
      setTotalLogs(0);
    }
  }, [filter, searchQuery, selectedLevel, selectedStatus, startDate, endDate]);

  // Load summaries data
  const loadSummaries = useCallback(async () => {
    try {
      const summariesFilter = {
        repoId: filter.repoId,
        groupName: filter.groupName,
        status: selectedStatus || undefined
      };
      console.log('Loading scan summaries with filter:', summariesFilter);
      const summariesResponse = await scanLogsApi.getSummaries(summariesFilter);
      console.log('Scan summaries loaded:', summariesResponse);
      setSummaries(summariesResponse.summaries || []);
    } catch (err) {
      console.error('Failed to load scan summaries:', err);
      setError('Failed to load scan summaries');
      setSummaries([]);
    }
  }, [filter.repoId, filter.groupName, selectedStatus]);

  // Initial load
  useEffect(() => {
    console.log('ScanLogsPage component mounted - starting initial data load');
    const loadData = async () => {
      setLoading(true);
      setError(null);

      try {
        await Promise.all([
          loadOverview(),
          loadLogs(),
          loadSummaries()
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [loadOverview, loadLogs, loadSummaries]);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh) {
      console.log('Auto-refresh disabled');
      return;
    }

    console.log(`Starting auto-refresh with interval: ${refreshInterval}s`);
    const interval = setInterval(async () => {
      console.log('Auto-refresh triggered for Settings scan logs page');
      setRefreshing(true);
      try {
        await Promise.all([
          loadOverview(),
          loadLogs(),
          loadSummaries()
        ]);
        console.log('Auto-refresh completed successfully');
      } catch (error) {
        console.error('Auto-refresh failed:', error);
      } finally {
        setRefreshing(false);
      }
    }, refreshInterval * 1000);

    return () => {
      console.log('Stopping auto-refresh for Settings scan logs page');
      clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval, loadOverview, loadLogs, loadSummaries]);

  // Handle search
  const handleSearch = useCallback(() => {
    setFilter(prev => ({ ...prev, page: 1 }));
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof ScanLogFilter, value: any) => {
    setFilter(prev => ({ ...prev, [key]: value, page: 1 }));
  }, []);

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setFilter(prev => ({ ...prev, page: newPage }));
  }, []);

  // Manual refresh
  const handleRefresh = useCallback(async () => {
    console.log('Manual refresh triggered for Settings scan logs page');
    setRefreshing(true);
    try {
      console.log('Starting manual refresh - loading overview, logs, and summaries');
      await Promise.all([
        loadOverview(),
        loadLogs(),
        loadSummaries()
      ]);
      console.log('Manual refresh completed successfully');
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setRefreshing(false);
    }
  }, [loadOverview, loadLogs, loadSummaries]);

  // Clear filters
  const clearFilters = useCallback(() => {
    setSearchQuery('');
    setSelectedLevel('');
    setSelectedStatus('');
    setStartDate('');
    setEndDate('');
    setFilter({ page: 1, pageSize: 50 });
  }, []);

  // Export logs
  const handleExport = useCallback(async (format: 'json' | 'csv' | 'txt') => {
    try {
      // Get all logs for export (no pagination)
      const exportFilter = {
        ...filter,
        searchQuery: searchQuery || undefined,
        level: selectedLevel || undefined,
        status: selectedStatus || undefined,
        startTime: startDate || undefined,
        endTime: endDate || undefined,
        page: 1,
        pageSize: 10000 // Large number to get all logs
      };

      const response = await scanLogsApi.getLogs(exportFilter);
      const logs = response.logs;

      let content = '';
      let filename = '';
      let mimeType = '';

      switch (format) {
        case 'json':
          content = JSON.stringify(logs, null, 2);
          filename = `scan-logs-${new Date().toISOString().split('T')[0]}.json`;
          mimeType = 'application/json';
          break;

        case 'csv':
          const headers = ['Timestamp', 'Level', 'Repository', 'Group', 'Step', 'Message', 'Details', 'Error', 'Duration'];
          const csvRows = [headers.join(',')];

          logs.forEach(log => {
            const row = [
              log.timestamp,
              log.level,
              log.repoId,
              log.groupName,
              log.step || '',
              `"${log.message.replace(/"/g, '""')}"`,
              `"${(log.details || '').replace(/"/g, '""')}"`,
              `"${(log.error || '').replace(/"/g, '""')}"`,
              log.duration ? formatDuration(log.duration) : ''
            ];
            csvRows.push(row.join(','));
          });

          content = csvRows.join('\n');
          filename = `scan-logs-${new Date().toISOString().split('T')[0]}.csv`;
          mimeType = 'text/csv';
          break;

        case 'txt':
          content = logs.map(log => {
            let line = `[${formatTimestamp(log.timestamp)}] ${log.level.toUpperCase()} - ${log.repoId}/${log.groupName}`;
            if (log.step) line += ` (${log.step})`;
            line += `\n  ${log.message}`;
            if (log.details) line += `\n  Details: ${log.details}`;
            if (log.error) line += `\n  Error: ${log.error}`;
            if (log.duration) line += `\n  Duration: ${formatDuration(log.duration)}`;
            return line;
          }).join('\n\n');
          filename = `scan-logs-${new Date().toISOString().split('T')[0]}.txt`;
          mimeType = 'text/plain';
          break;
      }

      // Create and download file
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (err) {
      console.error('Failed to export logs:', err);
      setError('Failed to export logs');
    }
  }, [filter, searchQuery, selectedLevel, selectedStatus, startDate, endDate]);

  // Cancel scan
  const handleCancelScan = useCallback(async (repoId: string, groupName: string) => {
    try {
      await scanLogsApi.cancelScan(repoId, groupName);
      // Refresh data after cancellation
      await Promise.all([
        loadOverview(),
        loadLogs(),
        loadSummaries()
      ]);
    } catch (err) {
      console.error('Failed to cancel scan:', err);
      setError('Failed to cancel scan');
    }
  }, [loadOverview, loadLogs, loadSummaries]);

  // Clear logs
  const handleClearLogs = useCallback(async (scanId?: string) => {
    try {
      if (scanId) {
        await scanLogsApi.clearScanLogs(scanId);
      } else {
        // Clear all logs - we'll need to implement this endpoint
        // For now, just refresh to show the current state
      }

      // Refresh data after clearing
      await Promise.all([
        loadOverview(),
        loadLogs(),
        loadSummaries()
      ]);
    } catch (err) {
      console.error('Failed to clear logs:', err);
      setError('Failed to clear logs');
    }
  }, [loadOverview, loadLogs, loadSummaries]);

  // Get status icon
  const getStatusIcon = (status: ScanStatus) => {
    switch (status) {
      case 'queued':
        return <Clock className="h-4 w-4" />;
      case 'running':
        return <Loader className="h-4 w-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      case 'cancelled':
        return <X className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  // Get log level icon
  const getLogLevelIcon = (level: ScanLogLevel) => {
    switch (level) {
      case 'info':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'error':
        return <XCircle className="h-4 w-4" />;
      case 'debug':
        return <Search className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading scan logs...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Usage Scanning Logs</h1>
          <p className="text-gray-600">Monitor and review usage scanning activities across all repositories</p>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              console.log('Refresh button clicked!');
              handleRefresh();
            }}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <div className="flex items-center space-x-2">
            <label className="text-sm">Auto-refresh:</label>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            {autoRefresh && (
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(Number(e.target.value))}
                className="text-sm border rounded px-2 py-1"
              >
                <option value={10}>10s</option>
                <option value={30}>30s</option>
                <option value={60}>1m</option>
                <option value={300}>5m</option>
              </select>
            )}
          </div>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center text-red-700">
              <XCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Cards */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Scans</p>
                  <p className="text-2xl font-bold">{overview.totalScans}</p>
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Search className="h-4 w-4 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Scans</p>
                  <p className="text-2xl font-bold text-blue-600">{overview.activeScans}</p>
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Loader className="h-4 w-4 text-blue-600 animate-spin" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{overview.completedScans}</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Failed</p>
                  <p className="text-2xl font-bold text-red-600">{overview.failedScans}</p>
                </div>
                <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
                  <XCircle className="h-4 w-4 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="logs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="logs">Detailed Logs</TabsTrigger>
          <TabsTrigger value="summaries">Scan Summaries</TabsTrigger>
          <TabsTrigger value="errors">Top Errors</TabsTrigger>
        </TabsList>

        <TabsContent value="logs" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Search</label>
                  <div className="relative">
                    <Input
                      placeholder="Search logs..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute right-1 top-1 h-6 w-6 p-0"
                      onClick={handleSearch}
                    >
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Level</label>
                  <select
                    value={selectedLevel}
                    onChange={(e) => setSelectedLevel(e.target.value as ScanLogLevel | '')}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="">All Levels</option>
                    <option value="info">Info</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                    <option value="debug">Debug</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value as ScanStatus | '')}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="">All Statuses</option>
                    <option value="queued">Queued</option>
                    <option value="running">Running</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Start Date</label>
                  <Input
                    type="datetime-local"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">End Date</label>
                  <Input
                    type="datetime-local"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>

                <div className="flex items-end">
                  <Button variant="outline" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Logs List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Scan Logs ({totalLogs} total)</CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleExport('json')}>
                      Export as JSON
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('csv')}>
                      Export as CSV
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('txt')}>
                      Export as Text
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {logs.map((log) => (
                  <div
                    key={log.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={`${LogLevelColors[log.level]} bg-transparent border`}>
                            <span className="flex items-center">
                              {getLogLevelIcon(log.level)}
                              <span className="ml-1">{getLogLevelText(log.level)}</span>
                            </span>
                          </Badge>

                          {log.step && (
                            <Badge variant="outline">
                              {log.step}
                            </Badge>
                          )}

                          {log.sourceName && (
                            <Badge variant="secondary">
                              {log.sourceName}
                            </Badge>
                          )}
                        </div>

                        <p className="font-medium mb-1">{log.message}</p>

                        {log.details && (
                          <p className="text-sm text-gray-600 mb-2">{log.details}</p>
                        )}

                        {log.error && (
                          <p className="text-sm text-red-600 bg-red-50 p-2 rounded mb-2">
                            {log.error}
                          </p>
                        )}

                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>{log.repoId} / {log.groupName}</span>
                          <span>{formatTimestamp(log.timestamp)}</span>
                          {log.duration && (
                            <span>Duration: {formatDuration(log.duration)}</span>
                          )}
                        </div>
                      </div>

                      {log.progress && (
                        <div className="ml-4 text-right">
                          <div className="text-sm font-medium">
                            {log.progress.current} / {log.progress.total}
                          </div>
                          <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all"
                              style={{ width: `${log.progress.percentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {logs.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No scan logs found matching the current filters.
                  </div>
                )}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-600">
                    Page {filter.page} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={filter.page === 1}
                      onClick={() => handlePageChange(filter.page! - 1)}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={filter.page === totalPages}
                      onClick={() => handlePageChange(filter.page! + 1)}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="summaries">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Scan Summaries</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleClearLogs()}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear All Logs
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {summaries.map((summary) => (
                  <div
                    key={summary.scanId}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge className={`${ScanStatusColors[summary.status]} border-0`}>
                          <span className="flex items-center">
                            {getStatusIcon(summary.status)}
                            <span className="ml-1">{getScanStatusText(summary.status)}</span>
                          </span>
                        </Badge>
                        <span className="font-medium">{summary.repoId} / {summary.groupName}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {formatRelativeTime(summary.startTime)}
                        </span>
                        <div className="flex items-center space-x-2">
                          {(summary.status === 'running' || summary.status === 'queued') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleCancelScan(summary.repoId, summary.groupName)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <X className="h-4 w-4 mr-1" />
                              Cancel
                            </Button>
                          )}
                          {(summary.status === 'completed' || summary.status === 'failed' || summary.status === 'cancelled') && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleClearLogs(summary.scanId)}
                              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Sources:</span>
                        <span className="ml-1 font-medium">
                          {summary.sourcesScanned} / {summary.totalSources}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Results:</span>
                        <span className="ml-1 font-medium">{summary.resultsFound}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Errors:</span>
                        <span className="ml-1 font-medium text-red-600">{summary.errorCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <span className="ml-1 font-medium">
                          {summary.duration ? formatDuration(summary.duration) : 'N/A'}
                        </span>
                      </div>
                    </div>

                    {summary.sourceStatuses && summary.sourceStatuses.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <div className="text-sm font-medium mb-2">Source Status:</div>
                        <div className="flex flex-wrap gap-2">
                          {summary.sourceStatuses.map((source) => (
                            <Badge
                              key={source.sourceId}
                              className={`${ScanStatusColors[source.status]} border-0 text-xs`}
                            >
                              {source.sourceName}: {getScanStatusText(source.status)}
                              {source.resultCount > 0 && ` (${source.resultCount})`}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {summaries.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No scan summaries found.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors">
          <Card>
            <CardHeader>
              <CardTitle>Top Errors</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(overview?.topErrors || []).map((error, index) => (
                  <div
                    key={index}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge variant="destructive">
                            Error #{index + 1}
                          </Badge>
                          <Badge variant="outline">
                            {error.count} occurrence{error.count > 1 ? 's' : ''}
                          </Badge>
                        </div>
                        <p className="font-medium text-red-700 mb-2">{error.error}</p>
                        <div className="text-sm text-gray-600">
                          <span>Last occurred: {formatRelativeTime(error.lastOccurred)}</span>
                          {error.affectedGroups && error.affectedGroups.length > 0 && (
                            <span className="ml-4">
                              Affected groups: {error.affectedGroups.join(', ')}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {(!overview?.topErrors || overview.topErrors.length === 0) && (
                  <div className="text-center py-8 text-gray-500">
                    No errors found.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ScanLogsPage;
