package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// UsageSourceManager defines the interface for managing usage sources
type UsageSourceManager interface {
	// CRUD operations
	CreateSource(source models.UsageSource) (models.UsageSource, error)
	GetSource(id string) (models.UsageSource, error)
	GetAllSources() ([]models.UsageSource, error)
	GetActiveSourcesByType(sourceType models.SourceType) ([]models.UsageSource, error)
	UpdateSource(source models.UsageSource) (models.UsageSource, error)
	DeleteSource(id string) error

	// Configuration management
	LoadSources() error
	SaveSources() error

	// Source validation
	ValidateSource(source models.UsageSource) error
	TestSourceConnection(id string) error

	// Cache management
	InvalidateCache()
}

// UsageSourceManagerImpl implements the UsageSourceManager interface
type UsageSourceManagerImpl struct {
	sources     map[string]models.UsageSource
	sourcesFile string
	mutex       sync.RWMutex
}

// NewUsageSourceManager creates a new usage source manager
func NewUsageSourceManager(dataDir string) *UsageSourceManagerImpl {
	// Create usage sources directory if it doesn't exist
	usageSourcesDir := filepath.Join(dataDir, "usage-sources")
	if err := os.MkdirAll(usageSourcesDir, 0755); err != nil {
		log.Printf("Warning: Failed to create usage sources directory: %v", err)
	}

	sourcesFile := filepath.Join(usageSourcesDir, "sources.json")

	manager := &UsageSourceManagerImpl{
		sources:     make(map[string]models.UsageSource),
		sourcesFile: sourcesFile,
	}

	// Load existing sources
	if err := manager.LoadSources(); err != nil {
		log.Printf("Warning: Failed to load usage sources: %v", err)
	}

	return manager
}

// CreateSource creates a new usage source
func (m *UsageSourceManagerImpl) CreateSource(source models.UsageSource) (models.UsageSource, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Validate the source (internal method that doesn't acquire lock)
	if err := m.validateSourceInternal(source); err != nil {
		return models.UsageSource{}, fmt.Errorf("validation failed: %w", err)
	}

	// Generate ID and timestamps
	source.ID = uuid.New().String()
	source.CreatedAt = time.Now()
	source.UpdatedAt = time.Now()

	// Store the source
	m.sources[source.ID] = source

	// Save to file
	if err := m.saveSources(); err != nil {
		// Remove from memory if save failed
		delete(m.sources, source.ID)
		return models.UsageSource{}, fmt.Errorf("failed to save source: %w", err)
	}

	log.Printf("Created usage source: %s (%s)", source.Name, source.ID)
	return source, nil
}

// GetSource retrieves a usage source by ID
func (m *UsageSourceManagerImpl) GetSource(id string) (models.UsageSource, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	source, exists := m.sources[id]
	if !exists {
		return models.UsageSource{}, fmt.Errorf("source not found: %s", id)
	}

	return source, nil
}

// GetAllSources retrieves all usage sources
func (m *UsageSourceManagerImpl) GetAllSources() ([]models.UsageSource, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sources := make([]models.UsageSource, 0, len(m.sources))
	for _, source := range m.sources {
		sources = append(sources, source)
	}

	return sources, nil
}

// GetActiveSourcesByType retrieves active sources of a specific type
func (m *UsageSourceManagerImpl) GetActiveSourcesByType(sourceType models.SourceType) ([]models.UsageSource, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var sources []models.UsageSource
	for _, source := range m.sources {
		if source.Type == sourceType && source.IsActive {
			sources = append(sources, source)
		}
	}

	return sources, nil
}

// UpdateSource updates an existing usage source
func (m *UsageSourceManagerImpl) UpdateSource(source models.UsageSource) (models.UsageSource, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if source exists
	existing, exists := m.sources[source.ID]
	if !exists {
		return models.UsageSource{}, fmt.Errorf("source not found: %s", source.ID)
	}

	// Validate the updated source (internal method that doesn't acquire lock)
	if err := m.validateSourceInternal(source); err != nil {
		return models.UsageSource{}, fmt.Errorf("validation failed: %w", err)
	}

	// Preserve creation time and update timestamp
	source.CreatedAt = existing.CreatedAt
	source.UpdatedAt = time.Now()

	// Store the updated source
	m.sources[source.ID] = source

	// Save to file
	if err := m.saveSources(); err != nil {
		// Restore original source if save failed
		m.sources[source.ID] = existing
		return models.UsageSource{}, fmt.Errorf("failed to save source: %w", err)
	}

	log.Printf("Updated usage source: %s (%s)", source.Name, source.ID)
	return source, nil
}

// DeleteSource deletes a usage source
func (m *UsageSourceManagerImpl) DeleteSource(id string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if source exists
	source, exists := m.sources[id]
	if !exists {
		return fmt.Errorf("source not found: %s", id)
	}

	// Remove from memory
	delete(m.sources, id)

	// Save to file
	if err := m.saveSources(); err != nil {
		// Restore source if save failed
		m.sources[id] = source
		return fmt.Errorf("failed to save sources: %w", err)
	}

	log.Printf("Deleted usage source: %s (%s)", source.Name, id)
	return nil
}

// LoadSources loads sources from file
func (m *UsageSourceManagerImpl) LoadSources() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if file exists
	if _, err := os.Stat(m.sourcesFile); os.IsNotExist(err) {
		log.Printf("Usage sources file does not exist, starting with empty sources: %s", m.sourcesFile)
		return nil
	}

	// Read file
	data, err := os.ReadFile(m.sourcesFile)
	if err != nil {
		return fmt.Errorf("failed to read sources file: %w", err)
	}

	// Parse JSON
	var sourcesList models.UsageSourceList
	if err := json.Unmarshal(data, &sourcesList); err != nil {
		return fmt.Errorf("failed to parse sources file: %w", err)
	}

	// Load sources into memory
	m.sources = make(map[string]models.UsageSource)
	for _, source := range sourcesList.Sources {
		m.sources[source.ID] = source
	}

	log.Printf("Loaded %d usage sources from file", len(m.sources))
	return nil
}

// SaveSources saves sources to file
func (m *UsageSourceManagerImpl) SaveSources() error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.saveSources()
}

// saveSources saves sources to file (internal method, assumes lock is held)
func (m *UsageSourceManagerImpl) saveSources() error {
	// Convert sources to list
	sources := make([]models.UsageSource, 0, len(m.sources))
	for _, source := range m.sources {
		sources = append(sources, source)
	}

	sourcesList := models.UsageSourceList{
		Sources: sources,
	}

	// Marshal to JSON
	data, err := json.MarshalIndent(sourcesList, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal sources: %w", err)
	}

	// Create directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(m.sourcesFile), 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// Write to file
	if err := os.WriteFile(m.sourcesFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write sources file: %w", err)
	}

	return nil
}

// ValidateSource validates a usage source
func (m *UsageSourceManagerImpl) ValidateSource(source models.UsageSource) error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.validateSourceInternal(source)
}

// validateSourceInternal validates a usage source (internal method, assumes lock is held)
func (m *UsageSourceManagerImpl) validateSourceInternal(source models.UsageSource) error {
	// Use the model's validation method
	if err := source.Validate(); err != nil {
		return err
	}

	// Additional business logic validation
	// Check for duplicate names (excluding the source being updated)
	for id, existing := range m.sources {
		if id != source.ID && existing.Name == source.Name {
			return models.NewValidationError("name", "source name already exists")
		}
	}

	return nil
}

// TestSourceConnection tests the connection to a usage source
func (m *UsageSourceManagerImpl) TestSourceConnection(id string) error {
	source, err := m.GetSource(id)
	if err != nil {
		return err
	}

	// TODO: Implement actual connection testing based on source type
	// This would involve creating the appropriate source handler and testing connectivity
	log.Printf("Testing connection for source: %s (%s)", source.Name, source.Type)

	// For now, just validate the configuration
	return m.ValidateSource(source)
}

// InvalidateCache invalidates any cached data (placeholder for future caching)
func (m *UsageSourceManagerImpl) InvalidateCache() {
	// Currently no caching implemented, but this method provides the interface
	// for future caching optimizations
	log.Println("Cache invalidated for usage sources")
}
